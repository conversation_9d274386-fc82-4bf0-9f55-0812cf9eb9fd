import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'dart:developer';

/// Extension to add safe state updates to any StateNotifier
extension SafeStateUpdates<T> on StateNotifier<T> {
  
  /// Safely update state only if mounted
  void safeSetState(T newState) {
    if (mounted) {
      state = newState;
    } else {
      log("Warning: Attempted to update disposed StateNotifier of type ${runtimeType}");
    }
  }

  /// Safely execute async operation and update state
  Future<void> safeAsyncUpdate(
    Future<T> Function() operation, {
    T? fallbackState,
    String? errorContext,
  }) async {
    try {
      final result = await operation();
      
      // Check if still mounted before updating
      if (mounted) {
        state = result;
      }
    } catch (e) {
      final context = errorContext ?? "Async operation";
      log("$context failed: $e");
      
      // Only set fallback if mounted and fallback provided
      if (mounted && fallbackState != null) {
        state = fallbackState;
      }
    }
  }

  /// Check if safe to update with logging
  bool get isSafeToUpdate {
    if (!mounted) {
      log("StateNotifier ${runtimeType} is disposed, skipping update");
      return false;
    }
    return true;
  }
}

/// Mixin for StateNotifiers that need safe async operations
mixin SafeAsyncNotifier<T> on StateNotifier<T> {
  
  /// Execute async operation safely
  Future<void> executeAsync(
    Future<void> Function() operation, {
    String? errorMessage,
  }) async {
    try {
      await operation();
    } catch (e) {
      if (errorMessage != null) {
        log("$errorMessage: $e");
      }
    }
  }

  /// Fetch data safely with automatic state management
  Future<void> safeFetch<R>(
    Future<R> Function() fetchOperation,
    T Function(R data) stateMapper, {
    T? fallbackState,
    String? errorContext,
  }) async {
    try {
      final data = await fetchOperation();
      
      if (mounted) {
        state = stateMapper(data);
      }
    } catch (e) {
      final context = errorContext ?? "Data fetch";
      log("$context error: $e");
      
      if (mounted && fallbackState != null) {
        state = fallbackState;
      }
    }
  }
}

/// Quick fix function to wrap any state assignment
T? safeMountedUpdate<T>(StateNotifier notifier, T newState) {
  if (notifier.mounted) {
    return newState;
  } else {
    log("Skipped update on disposed ${notifier.runtimeType}");
    return null;
  }
}
