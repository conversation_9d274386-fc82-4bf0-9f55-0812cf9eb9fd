import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:healo/services/firestore_service.dart';
import 'package:firebase_auth/firebase_auth.dart';

// Create a provider that forces refresh when auth state changes
final authStateProvider = StreamProvider<User?>((ref) {
  return FirebaseAuth.instance.authStateChanges();
});

// Make user providers depend on auth state
final userNameProvider = FutureProvider<String?>((ref) async {
  // Watch auth state to refresh when it changes
  ref.watch(authStateProvider);
  final user = await FirestoreService().getUser();
  return user?.name;
});

final userGenderProvider = FutureProvider<String?>((ref) async {
  // Watch auth state to refresh when it changes
  ref.watch(authStateProvider);
  final user = await FirestoreService().getUser();
  return user?.gender;
});

final FirebaseAuth auth = FirebaseAuth.instance;
String? get _uid => auth.currentUser?.phoneNumber;
final userPhoneProvider = FutureProvider<String?>((ref) async {
  // Watch auth state to refresh when it changes
  ref.watch(authStateProvider);
  return _uid;
});
