import 'dart:developer';

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:healo/services/firestore_service.dart';
import 'package:healo/providers/user_provider.dart';

// Provider for kidney history data
final kidneyHistoryProvider = StateNotifierProvider<KidneyHistoryNotifier, Map<String, Map<String, dynamic>>>(
  (ref) => KidneyHistoryNotifier(FirestoreService(), ref),
);

class KidneyHistoryNotifier extends StateNotifier<Map<String, Map<String, dynamic>>> {
  final FirestoreService _firestoreService;
  final Ref _ref;

  KidneyHistoryNotifier(this._firestoreService, this._ref) : super({}) {
    // Watch auth state to refresh data when user changes
    _ref.listen(authStateProvider, (previous, next) {
      next.when(
        data: (user) {
          if (user != null) {
            // User logged in, fetch data
            fetchKidneyHistory();
          } else {
            // User logged out, clear data
            state = {};
          }
        },
        loading: () {},
        error: (error, stack) {
          log("Auth state error in kidney provider: $error");
        },
      );
    });
  }

  Future<void> addKidneyReading(String date, Map<String, dynamic> reading) async {
    try {
      await _firestoreService.addKidneyReading(date, reading);
      await fetchKidneyHistory(); // Refresh data after adding a new reading
    } catch (e) {
      log("Error adding kidney reading: $e");
    }
  }

  Future<void> addKidneySymptoms(String date, Map<String, dynamic> symptoms) async {
    try {
      await _firestoreService.addKidneySymptoms(date, symptoms);
      await fetchKidneyHistory(); // Refresh data after adding symptoms
    } catch (e) {
      log("Error adding kidney symptoms: $e");
    }
  }

  Future<void> fetchKidneyHistory() async {
    try {
      final data = await _firestoreService.fetchKidneyHistory();
      final kidneyHistory = Map<String, dynamic>.from(data['history'] ?? {});

      final Map<String, Map<String, dynamic>> formattedData = {};

      kidneyHistory.forEach((dateKey, dayData) {
        Map<String, dynamic> dateData = {};

        if (dayData['readings'] != null) {
          dateData['readings'] = List<Map<String, dynamic>>.from(dayData['readings']);
        }

        if (dayData['symptoms'] != null) {
          dateData['symptoms'] = List<Map<String, dynamic>>.from(dayData['symptoms']);
        }

        formattedData[dateKey] = dateData;
      });

      state = formattedData;
    } catch (e) {
      log("Error fetching kidney history: $e");
      state = {};
    }
  }
}

// Provider for latest kidney readings
final latestKidneyReadingsProvider = Provider<Map<String, dynamic>?>((ref) {
  final kidneyData = ref.watch(kidneyHistoryProvider);

  if (kidneyData.isEmpty) return null;

  final sortedDates = kidneyData.keys.toList()
    ..sort((a, b) => _parseDate(a).compareTo(_parseDate(b)));

  if (sortedDates.isEmpty) return null;

  final latestDate = sortedDates.last;
  final readings = kidneyData[latestDate]?['readings'] as List<dynamic>?;

  if (readings == null || readings.isEmpty) return null;

  return readings.last as Map<String, dynamic>;
});

// Provider for previous kidney readings
final previousKidneyReadingsProvider = Provider<Map<String, dynamic>?>((ref) {
  final kidneyData = ref.watch(kidneyHistoryProvider);

  if (kidneyData.isEmpty) return null;

  final sortedDates = kidneyData.keys.toList()
    ..sort((a, b) => _parseDate(a).compareTo(_parseDate(b)));

  if (sortedDates.isEmpty) return null;

  // If there's only one date with multiple readings, use the second-to-last reading
  if (sortedDates.length == 1) {
    final readings = kidneyData[sortedDates[0]]?['readings'] as List<dynamic>?;
    if (readings != null && readings.length > 1) {
      return readings[readings.length - 2] as Map<String, dynamic>;
    }
    return null;
  }

  // If there are multiple dates, use the last reading from the second-to-last date
  final previousDate = sortedDates[sortedDates.length - 2];
  final readings = kidneyData[previousDate]?['readings'] as List<dynamic>?;

  if (readings == null || readings.isEmpty) return null;

  return readings.last as Map<String, dynamic>;
});

// Helper function to parse date string
DateTime _parseDate(String dateKey) {
  final parts = dateKey.split('-');
  return DateTime(
    int.parse(parts[2]),
    int.parse(parts[1]),
    int.parse(parts[0]),
  );
}

// Provider for kidney symptoms
final kidneySymptomProvider = Provider<List<Map<String, dynamic>>>((ref) {
  final kidneyData = ref.watch(kidneyHistoryProvider);

  if (kidneyData.isEmpty) return [];

  final sortedDates = kidneyData.keys.toList()
    ..sort((a, b) => _parseDate(a).compareTo(_parseDate(b)));

  if (sortedDates.isEmpty) return [];

  final latestDate = sortedDates.last;
  final symptoms = kidneyData[latestDate]?['symptoms'] as List<dynamic>?;

  if (symptoms == null || symptoms.isEmpty) return [];

  return List<Map<String, dynamic>>.from(symptoms);
});

// Provider for kidney health score
final kidneyHealthScoreProvider = Provider<double?>((ref) {
  final latestReadings = ref.watch(latestKidneyReadingsProvider);

  // Return null if no readings are available
  if (latestReadings == null) return null;

  // Get all the metrics
  final gfr = latestReadings['gfr'] as double?;
  final creatinine = latestReadings['creatinine'] as double?;
  final albumin = latestReadings['albumin'] as double?;
  final bun = latestReadings['bun'] as double?;

  // If we don't have at least GFR, return null
  if (gfr == null) return null;

  // Calculate individual scores
  int gfrScore = _calculateGfrScore(gfr);
  int creatinineScore = _calculateCreatinineScore(creatinine);
  int albuminScore = _calculateAlbuminScore(albumin);
  int bunScore = _calculateBunScore(bun);

  // Calculate total score (out of 100)
  double totalScore = (gfrScore + creatinineScore + albuminScore + bunScore).toDouble();

  // Return the score as a percentage (0-100)
  return totalScore;
});

// Helper function to calculate GFR score (0-25)
int _calculateGfrScore(double? gfr) {
  if (gfr == null) return 0;

  if (gfr >= 90) {
    return 25;
  } else if (gfr >= 60) {
    return 20;
  } else if (gfr >= 30) {
    return 15;
  } else if (gfr >= 15) {
    return 5;
  } else {
    return 0;
  }
}

// Helper function to calculate Creatinine score (0-25)
int _calculateCreatinineScore(double? creatinine) {
  if (creatinine == null) return 0;

  if (creatinine <= 1.2) {
    return 25;
  } else if (creatinine <= 2.0) {
    return 15;
  } else if (creatinine <= 3.5) {
    return 10;
  } else {
    return 5;
  }
}

// Helper function to calculate Albumin score (0-25)
int _calculateAlbuminScore(double? albumin) {
  if (albumin == null) return 0;

  if (albumin >= 4.0) {
    return 25;
  } else if (albumin >= 3.5) {
    return 20;
  } else if (albumin >= 3.0) {
    return 10;
  } else {
    return 5;
  }
}

// Helper function to calculate BUN score (0-25)
int _calculateBunScore(double? bun) {
  if (bun == null) return 0;

  if (bun <= 20) {
    return 25;
  } else if (bun <= 30) {
    return 15;
  } else if (bun <= 50) {
    return 10;
  } else {
    return 5;
  }
}
