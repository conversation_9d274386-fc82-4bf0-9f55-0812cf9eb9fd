import 'dart:developer';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:healo/services/firestore_service.dart';
import 'package:healo/providers/user_provider.dart';

final waterIntakeProvider =
    StateNotifierProvider<WaterIntakeNotifier, int>((ref) {
  return WaterIntakeNotifier(FirestoreService(), ref);
});

class WaterIntakeNotifier extends StateNotifier<int> {
  final FirestoreService _firestoreService;
  final Ref _ref;
  static const _localKey = 'daily_glasses';

  WaterIntakeNotifier(this._firestoreService, this._ref) : super(0) {
    _loadLocalValue();

    // Watch auth state to refresh data when user changes
    _ref.listen(authStateProvider, (previous, next) {
      next.when(
        data: (user) {
          if (user != null) {
            // User logged in, reload local value
            _loadLocalValue();
          } else {
            // User logged out, reset to 0
            state = 0;
            _clearLocalValue();
          }
        },
        loading: () {},
        error: (error, stack) {
          log("Auth state error in water intake provider: $error");
        },
      );
    });
  }

  Future<void> _loadLocalValue() async {
    final prefs = await SharedPreferences.getInstance();
    final savedValue = prefs.getInt(_localKey) ?? 0;
    state = savedValue;
  }

  Future<int> _loadTotalValue() async {
    final prefs = await SharedPreferences.getInstance();
    final savedValue = prefs.getInt("total_glasses") ?? 10;
    return savedValue;
  }

  Future<void> _saveLocalValue(int value) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt(_localKey, value);
  }

  Future<void> _clearLocalValue() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_localKey);
  }

  Future<void> setGlasses(int value) async {
    state = value;
    await _saveLocalValue(value);
    int total = await _loadTotalValue();

    _firestoreService.updateWaterIntake(value, total);
  }

  Future<void> increment() async {
    state += 1;
    await _saveLocalValue(state);
    int total = await _loadTotalValue();
    await _firestoreService.updateWaterIntake(state, total);
    log("Updated water intake to $state");
  }

  Future<void> decrement() async {
    state -= 1;
    await _saveLocalValue(state);
    int total = await _loadTotalValue();
    await _firestoreService.updateWaterIntake(state, total);
    log("Updated water intake to $state");
  }
}

final glassesPerDayProvider =
    StateNotifierProvider<GlassesPerDayNotifier, int>((ref) {
  return GlassesPerDayNotifier();
});

class GlassesPerDayNotifier extends StateNotifier<int> {
  static const _localKey = 'total_glasses';

  Future<void> _loadLocalValue() async {
    final prefs = await SharedPreferences.getInstance();
    final savedValue = prefs.getInt(_localKey) ?? 10;
    state = savedValue;
  }

  Future<void> _saveLocalValue(int value) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt(_localKey, value);
  }

  GlassesPerDayNotifier() : super(10) {
    _loadLocalValue();
  }

  Future<void> setGlasses(int value) async {
    state = value;
    await _saveLocalValue(value);
  }

  Future<void> increment() async {
    if (state < 10) state++;
    await _saveLocalValue(state);
  }

  Future<void> decrement() async {
    if (state > 0) state--;
    await _saveLocalValue(state);
  }
}

class WeeklyAverageNotifier extends StateNotifier<double> {
  final FirestoreService _firestoreService;
  final Ref _ref;

  WeeklyAverageNotifier(this._firestoreService, this._ref) : super(0.0) {
    // Watch auth state to refresh data when user changes
    _ref.listen(authStateProvider, (previous, next) {
      next.when(
        data: (user) {
          if (user != null) {
            // User logged in, fetch data
            fetchAverage();
          } else {
            // User logged out, clear data
            state = 0.0;
          }
        },
        loading: () {},
        error: (error, stack) {
          log("Auth state error in weekly average provider: $error");
        },
      );
    });
  }

  Future<void> fetchAverage() async {
    try {
      final average = await _firestoreService.getWaterIntakeAverage();
      state = average;
    } catch (e) {
      state = 0.0;
      log("Error fetching weekly average: $e");
    }
  }
}

final weeklyAverageProvider =
    StateNotifierProvider<WeeklyAverageNotifier, double>(
  (ref) => WeeklyAverageNotifier(FirestoreService(), ref),
);

class MonthlyAverageNotifier extends StateNotifier<double> {
  final FirestoreService _firestoreService;
  final Ref _ref;

  MonthlyAverageNotifier(this._firestoreService, this._ref) : super(0.0) {
    // Watch auth state to refresh data when user changes
    _ref.listen(authStateProvider, (previous, next) {
      next.when(
        data: (user) {
          if (user != null) {
            // User logged in, fetch data
            fetchAverage();
          } else {
            // User logged out, clear data
            state = 0.0;
          }
        },
        loading: () {},
        error: (error, stack) {
          log("Auth state error in monthly average provider: $error");
        },
      );
    });
  }

  Future<void> fetchAverage() async {
    try {
      final average = await _firestoreService.getMonthlyIntakeAverage();
      state = average;
    } catch (e) {
      state = 0.0;
      log("Error fetching monthly average: $e");
    }
  }
}

final monthlyAverageProvider =
    StateNotifierProvider<MonthlyAverageNotifier, double>(
  (ref) => MonthlyAverageNotifier(FirestoreService(), ref),
);

class BestWeekDayNotifier extends StateNotifier<int> {
  final FirestoreService _firestoreService;
  final Ref _ref;

  BestWeekDayNotifier(this._firestoreService, this._ref) : super(0) {
    // Watch auth state to refresh data when user changes
    _ref.listen(authStateProvider, (previous, next) {
      next.when(
        data: (user) {
          if (user != null) {
            fetchBestWeekDay();
          } else {
            state = 0;
          }
        },
        loading: () {},
        error: (error, stack) {
          log("Auth state error in best week day provider: $error");
        },
      );
    });
  }

  Future<void> fetchBestWeekDay() async {
    try {
      final bestWeekDay = await _firestoreService.getBestWeekDay();
      state = bestWeekDay;
    } catch (e) {
      log("Error fetching best week day: $e");
    }
  }
}

final bestWeekDayProvider = StateNotifierProvider<BestWeekDayNotifier, int>(
  (ref) => BestWeekDayNotifier(FirestoreService(), ref),
);

class BestMonthDayNotifier extends StateNotifier<int> {
  final FirestoreService _firestoreService;
  final Ref _ref;

  BestMonthDayNotifier(this._firestoreService, this._ref) : super(0) {
    // Watch auth state to refresh data when user changes
    _ref.listen(authStateProvider, (previous, next) {
      next.when(
        data: (user) {
          if (user != null) {
            fetchBestMonthDay();
          } else {
            state = 0;
          }
        },
        loading: () {},
        error: (error, stack) {
          log("Auth state error in best month day provider: $error");
        },
      );
    });
  }

  Future<void> fetchBestMonthDay() async {
    try {
      final bestMonthDay = await _firestoreService.getBestMonthDay();
      state = bestMonthDay;
    } catch (e) {
      log("Error fetching best month day: $e");
    }
  }
}

final bestMonthDayProvider = StateNotifierProvider<BestMonthDayNotifier, int>(
  (ref) => BestMonthDayNotifier(FirestoreService(), ref),
);

class StreakNotifier extends StateNotifier<int> {
  final FirestoreService _firestoreService;
  final Ref _ref;

  StreakNotifier(this._firestoreService, this._ref) : super(0) {
    // Watch auth state to refresh data when user changes
    _ref.listen(authStateProvider, (previous, next) {
      next.when(
        data: (user) {
          if (user != null) {
            fetchStreak();
          } else {
            state = 0;
          }
        },
        loading: () {},
        error: (error, stack) {
          log("Auth state error in streak provider: $error");
        },
      );
    });
  }

  Future<void> fetchStreak() async {
    try {
      final streak = await _firestoreService.getStreak();
      state = streak;
    } catch (e) {
      log("Error fetching streak: $e");
    }
  }
}

final streakProvider = StateNotifierProvider<StreakNotifier, int>(
  (ref) => StreakNotifier(FirestoreService(), ref),
);

class WeeklyGraphNotifier
    extends StateNotifier<Map<String, Map<String, dynamic>>> {
  final FirestoreService _firestoreService;
  final Ref _ref;

  WeeklyGraphNotifier(this._firestoreService, this._ref) : super({}) {
    // Watch auth state to refresh data when user changes
    _ref.listen(authStateProvider, (previous, next) {
      next.when(
        data: (user) {
          if (user != null) {
            fetchWeeklyGraphData();
          } else {
            state = {};
          }
        },
        loading: () {},
        error: (error, stack) {
          log("Auth state error in weekly graph provider: $error");
        },
      );
    });
  }

  Future<void> fetchWeeklyGraphData() async {
    try {
      final doc = await _firestoreService.getWaterIntakeData();

      final intakeData = Map<String, dynamic>.from(doc['intake_data'] ?? {});
      final now = DateTime.now();
      final Map<String, Map<String, dynamic>> formattedData = {};

      for (int i = 0; i < 7; i++) {
        final date = now.subtract(Duration(days: i));
        final dateKey = "${date.day}-${date.month}-${date.year}";
        final dayData = intakeData[dateKey];

        if (dayData != null) {
          formattedData[dateKey] = {
            'day': dayData['day'],
            'glasses': dayData['glasses'],
          };
        }
      }

      state = formattedData;
      log(formattedData.toString());
    } catch (e) {
      log("Error fetching weekly graph data: $e");
    }
  }
}

final weeklyGraphProvider = StateNotifierProvider<WeeklyGraphNotifier,
    Map<String, Map<String, dynamic>>>(
  (ref) => WeeklyGraphNotifier(FirestoreService(), ref),
);

class MonthlyGraphNotifier
    extends StateNotifier<Map<String, Map<String, dynamic>>> {
  final FirestoreService _firestoreService;
  final Ref _ref;

  MonthlyGraphNotifier(this._firestoreService, this._ref) : super({}) {
    // Watch auth state to refresh data when user changes
    _ref.listen(authStateProvider, (previous, next) {
      next.when(
        data: (user) {
          if (user != null) {
            fetchMonthlyGraphData();
          } else {
            state = {};
          }
        },
        loading: () {},
        error: (error, stack) {
          log("Auth state error in monthly graph provider: $error");
        },
      );
    });
  }

  Future<void> fetchMonthlyGraphData() async {
    try {
      final doc = await _firestoreService.getWaterIntakeData();

      final intakeData = Map<String, dynamic>.from(doc['intake_data'] ?? {});
      final now = DateTime.now();
      final Map<String, Map<String, dynamic>> formattedData = {};

      for (int i = 0; i <= intakeData.length; i++) {
        final date = now.subtract(Duration(days: i));
        final dateKey = "${date.day}-${date.month}-${date.year}";
        final dayData = intakeData[dateKey];

        if (dayData != null) {
          formattedData[dateKey] = {
            'day': dayData['day'],
            'glasses': dayData['glasses'],
          };
        }
      }

      state = formattedData;
      log(formattedData.toString());
    } catch (e) {
      log("Error fetching weekly graph data: $e");
    }
  }
}

final monthlyGraphProvider = StateNotifierProvider<MonthlyGraphNotifier,
    Map<String, Map<String, dynamic>>>(
  (ref) => MonthlyGraphNotifier(FirestoreService(), ref),
);

final updatedTodayProvider = StateNotifierProvider<UpdatedTodayNotifier, bool>(
  (ref) => UpdatedTodayNotifier(FirestoreService(), ref),
);

class UpdatedTodayNotifier extends StateNotifier<bool> {
  final FirestoreService _firestoreService;
  final Ref _ref;

  UpdatedTodayNotifier(this._firestoreService, this._ref) : super(false) {
    // Watch auth state to refresh data when user changes
    _ref.listen(authStateProvider, (previous, next) {
      next.when(
        data: (user) {
          if (user != null) {
            updatedToday();
          } else {
            state = false;
          }
        },
        loading: () {},
        error: (error, stack) {
          log("Auth state error in updated today provider: $error");
        },
      );
    });
  }

  Future<void> updatedToday() async {
    try {
      final doc = await _firestoreService.getWaterIntakeData();

      final intakeData = Map<String, dynamic>.from(doc['intake_data'] ?? {});
      final now = DateTime.now();
      final String todayKey = "${now.day}-${now.month}-${now.year}";
      final formattedData = intakeData[todayKey];

      log(formattedData.toString());
      if (formattedData != null) {
        state = true;
      }
      log(state.toString());
    } catch (e) {
      log("Error fetching updated today data: $e");
    }
  }
}

final glassesTodayProvider = StateNotifierProvider<GlassesTodayNotifier, int>(
  (ref) => GlassesTodayNotifier(FirestoreService(), ref),
);

class GlassesTodayNotifier extends StateNotifier<int> {
  final FirestoreService _firestoreService;
  final Ref _ref;

  GlassesTodayNotifier(this._firestoreService, this._ref) : super(0) {
    // Watch auth state to refresh data when user changes
    _ref.listen(authStateProvider, (previous, next) {
      next.when(
        data: (user) {
          if (user != null) {
            fetchGlassesToday();
          } else {
            state = 0;
          }
        },
        loading: () {},
        error: (error, stack) {
          log("Auth state error in glasses today provider: $error");
        },
      );
    });
  }

  Future<void> fetchGlassesToday() async {
    try {
      final doc = await _firestoreService.getWaterIntakeData();

      final intakeData = Map<String, dynamic>.from(doc['intake_data'] ?? {});
      final now = DateTime.now();
      final String todayKey = "${now.day}-${now.month}-${now.year}";
      final formattedData = intakeData[todayKey];

      log(formattedData.toString());
      if (formattedData != null) {
        state = formattedData['glasses'];
      }
    } catch (e) {
      log("Error fetching glasses today data: $e");
    }
  }
}
