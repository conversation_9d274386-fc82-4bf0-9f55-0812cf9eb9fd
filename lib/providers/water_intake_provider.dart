import 'dart:developer';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:healo/services/firestore_service.dart';

final waterIntakeProvider =
    StateNotifierProvider<WaterIntakeNotifier, int>((ref) {
  return WaterIntakeNotifier(FirestoreService());
});

class WaterIntakeNotifier extends StateNotifier<int> {
  final FirestoreService _firestoreService;
  static const _localKey = 'daily_glasses';

  WaterIntakeNotifier(this._firestoreService) : super(0) {
    _loadLocalValue();
  }

  Future<void> _loadLocalValue() async {
    final prefs = await SharedPreferences.getInstance();
    final savedValue = prefs.getInt(_localKey) ?? 0;
    state = savedValue;
  }

  Future<int> _loadTotalValue() async {
    final prefs = await SharedPreferences.getInstance();
    final savedValue = prefs.getInt("total_glasses") ?? 10;
    return savedValue;
  }

  Future<void> _saveLocalValue(int value) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt(_localKey, value);
  }

  Future<void> setGlasses(int value) async {
    state = value;
    await _saveLocalValue(value);
    int total = await _loadTotalValue();

    _firestoreService.updateWaterIntake(value, total);
  }

  Future<void> increment() async {
    state += 1;
    await _saveLocalValue(state);
    int total = await _loadTotalValue();
    await _firestoreService.updateWaterIntake(state, total);
    log("Updated water intake to $state");
  }

  Future<void> decrement() async {
    state -= 1;
    await _saveLocalValue(state);
    int total = await _loadTotalValue();
    await _firestoreService.updateWaterIntake(state, total);
    log("Updated water intake to $state");
  }
}

final glassesPerDayProvider =
    StateNotifierProvider<GlassesPerDayNotifier, int>((ref) {
  return GlassesPerDayNotifier();
});

class GlassesPerDayNotifier extends StateNotifier<int> {
  static const _localKey = 'total_glasses';

  Future<void> _loadLocalValue() async {
    final prefs = await SharedPreferences.getInstance();
    final savedValue = prefs.getInt(_localKey) ?? 10;
    state = savedValue;
  }

  Future<void> _saveLocalValue(int value) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt(_localKey, value);
  }

  GlassesPerDayNotifier() : super(10) {
    _loadLocalValue();
  }

  Future<void> setGlasses(int value) async {
    state = value;
    await _saveLocalValue(value);
  }

  Future<void> increment() async {
    if (state < 10) state++;
    await _saveLocalValue(state);
  }

  Future<void> decrement() async {
    if (state > 0) state--;
    await _saveLocalValue(state);
  }
}

class WeeklyAverageNotifier extends StateNotifier<double> {
  final FirestoreService _firestoreService;

  WeeklyAverageNotifier(this._firestoreService) : super(0.0);

  Future<void> fetchAverage() async {
    try {
      final average = await _firestoreService.getWaterIntakeAverage();
      state = average;
    } catch (e) {
      state = 0.0;
      log("Error fetching weekly average: $e");
    }
  }
}

final weeklyAverageProvider =
    StateNotifierProvider<WeeklyAverageNotifier, double>(
  (ref) => WeeklyAverageNotifier(FirestoreService()),
);

class MonthlyAverageNotifier extends StateNotifier<double> {
  final FirestoreService _firestoreService;

  MonthlyAverageNotifier(this._firestoreService) : super(0.0);

  Future<void> fetchAverage() async {
    try {
      final average = await _firestoreService.getMonthlyIntakeAverage();
      state = average;
    } catch (e) {
      state = 0.0;
      log("Error fetching monthly average: $e");
    }
  }
}

final monthlyAverageProvider =
    StateNotifierProvider<MonthlyAverageNotifier, double>(
  (ref) => MonthlyAverageNotifier(FirestoreService()),
);

class BestWeekDayNotifier extends StateNotifier<int> {
  final FirestoreService _firestoreService;

  BestWeekDayNotifier(this._firestoreService) : super(0);

  Future<void> fetchBestWeekDay() async {
    try {
      final bestWeekDay = await _firestoreService.getBestWeekDay();
      state = bestWeekDay;
    } catch (e) {
      log("Error fetching best week day: $e");
    }
  }
}

final bestWeekDayProvider = StateNotifierProvider<BestWeekDayNotifier, int>(
  (ref) => BestWeekDayNotifier(FirestoreService()),
);

class BestMonthDayNotifier extends StateNotifier<int> {
  final FirestoreService _firestoreService;

  BestMonthDayNotifier(this._firestoreService) : super(0);

  Future<void> fetchBestMonthDay() async {
    try {
      final bestMonthDay = await _firestoreService.getBestMonthDay();
      state = bestMonthDay;
    } catch (e) {
      log("Error fetching best month day: $e");
    }
  }
}

final bestMonthDayProvider = StateNotifierProvider<BestMonthDayNotifier, int>(
  (ref) => BestMonthDayNotifier(FirestoreService()),
);

class StreakNotifier extends StateNotifier<int> {
  final FirestoreService _firestoreService;

  StreakNotifier(this._firestoreService) : super(0);

  Future<void> fetchStreak() async {
    try {
      final streak = await _firestoreService.getStreak();
      state = streak;
    } catch (e) {
      log("Error fetching streak: $e");
    }
  }
}

final streakProvider = StateNotifierProvider<StreakNotifier, int>(
  (ref) => StreakNotifier(FirestoreService()),
);

class WeeklyGraphNotifier
    extends StateNotifier<Map<String, Map<String, dynamic>>> {
  final FirestoreService _firestoreService;

  WeeklyGraphNotifier(this._firestoreService) : super({});

  Future<void> fetchWeeklyGraphData() async {
    try {
      final doc = await _firestoreService.getWaterIntakeData();

      final intakeData = Map<String, dynamic>.from(doc['intake_data'] ?? {});
      final now = DateTime.now();
      final Map<String, Map<String, dynamic>> formattedData = {};

      for (int i = 0; i < 7; i++) {
        final date = now.subtract(Duration(days: i));
        final dateKey = "${date.day}-${date.month}-${date.year}";
        final dayData = intakeData[dateKey];

        if (dayData != null) {
          formattedData[dateKey] = {
            'day': dayData['day'],
            'glasses': dayData['glasses'],
          };
        }
      }

      state = formattedData;
      log(formattedData.toString());
    } catch (e) {
      log("Error fetching weekly graph data: $e");
    }
  }
}

final weeklyGraphProvider = StateNotifierProvider<WeeklyGraphNotifier,
    Map<String, Map<String, dynamic>>>(
  (ref) => WeeklyGraphNotifier(FirestoreService()),
);

class MonthlyGraphNotifier
    extends StateNotifier<Map<String, Map<String, dynamic>>> {
  final FirestoreService _firestoreService;

  MonthlyGraphNotifier(this._firestoreService) : super({});

  Future<void> fetchMonthlyGraphData() async {
    try {
      final doc = await _firestoreService.getWaterIntakeData();

      final intakeData = Map<String, dynamic>.from(doc['intake_data'] ?? {});
      final now = DateTime.now();
      final Map<String, Map<String, dynamic>> formattedData = {};

      for (int i = 0; i <= intakeData.length; i++) {
        final date = now.subtract(Duration(days: i));
        final dateKey = "${date.day}-${date.month}-${date.year}";
        final dayData = intakeData[dateKey];

        if (dayData != null) {
          formattedData[dateKey] = {
            'day': dayData['day'],
            'glasses': dayData['glasses'],
          };
        }
      }

      state = formattedData;
      log(formattedData.toString());
    } catch (e) {
      log("Error fetching weekly graph data: $e");
    }
  }
}

final monthlyGraphProvider = StateNotifierProvider<MonthlyGraphNotifier,
    Map<String, Map<String, dynamic>>>(
  (ref) => MonthlyGraphNotifier(FirestoreService()),
);

final updatedTodayProvider = StateNotifierProvider<UpdatedTodayNotifier, bool>(
  (ref) => UpdatedTodayNotifier(FirestoreService()),
);

class UpdatedTodayNotifier extends StateNotifier<bool> {
  final FirestoreService _firestoreService;

  UpdatedTodayNotifier(this._firestoreService) : super(false);

  Future<void> updatedToday() async {
    try {
      final doc = await _firestoreService.getWaterIntakeData();

      final intakeData = Map<String, dynamic>.from(doc['intake_data'] ?? {});
      final now = DateTime.now();
      final String todayKey = "${now.day}-${now.month}-${now.year}";
      final formattedData = intakeData[todayKey];

      log(formattedData.toString());
      if (formattedData != null) {
        state = true;
      }
      log(state.toString());
    } catch (e) {
      log("Error fetching updated today data: $e");
    }
  }
}

final glassesTodayProvider = StateNotifierProvider<GlassesTodayNotifier, int>(
  (ref) => GlassesTodayNotifier(FirestoreService()),
);

class GlassesTodayNotifier extends StateNotifier<int> {
  final FirestoreService _firestoreService;

  GlassesTodayNotifier(this._firestoreService) : super(0);

  Future<void> fetchGlassesToday() async {
    try {
      final doc = await _firestoreService.getWaterIntakeData();

      final intakeData = Map<String, dynamic>.from(doc['intake_data'] ?? {});
      final now = DateTime.now();
      final String todayKey = "${now.day}-${now.month}-${now.year}";
      final formattedData = intakeData[todayKey];

      log(formattedData.toString());
      if (formattedData != null) {
        state = formattedData['glasses'];
      }
    } catch (e) {
      log("Error fetching glasses today data: $e");
    }
  }
}
