import 'dart:developer';

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:healo/services/firestore_service.dart';
import 'package:healo/providers/user_provider.dart';

// Provider for liver history data
final liverHistoryProvider = StateNotifierProvider<LiverHistoryNotifier, Map<String, Map<String, dynamic>>>(
  (ref) => LiverHistoryNotifier(FirestoreService(), ref),
);

// Notifier class for liver history
class LiverHistoryNotifier extends StateNotifier<Map<String, Map<String, dynamic>>> {
  final FirestoreService _firestoreService;
  final Ref _ref;

  LiverHistoryNotifier(this._firestoreService, this._ref) : super({}) {
    // Watch auth state to refresh data when user changes
    _ref.listen(authStateProvider, (previous, next) {
      next.when(
        data: (user) {
          if (user != null) {
            // User logged in, fetch data
            fetchLiverHistory();
          } else {
            // User logged out, clear data
            state = {};
          }
        },
        loading: () {},
        error: (error, stack) {
          log("Auth state error in liver provider: $error");
        },
      );
    });
  }

  // Add a new liver reading
  Future<void> addLiverReading(String date, Map<String, dynamic> reading) async {
    try {
      await _firestoreService.addLiverReading(date, reading);
      await fetchLiverHistory();
    } catch (e) {
      log("Error adding liver reading: $e");
      throw Exception("Failed to add liver reading: $e");
    }
  }

  // Fetch liver history
  Future<void> fetchLiverHistory() async {
    try {
      final data = await _firestoreService.fetchLiverHistory();
      if (data.containsKey('history')) {
        state = Map<String, Map<String, dynamic>>.from(data['history']);
      } else {
        state = {};
      }
    } catch (e) {
      log("Error fetching liver history: $e");
      state = {};
    }
  }
}

// Helper function to parse date strings
DateTime _parseDate(String dateStr) {
  final parts = dateStr.split('-');
  if (parts.length == 3) {
    return DateTime(
      int.parse(parts[2]),
      int.parse(parts[1]),
      int.parse(parts[0]),
    );
  }
  return DateTime.now();
}

// Provider for latest liver readings
final latestLiverReadingProvider = Provider<Map<String, dynamic>?>((ref) {
  final liverData = ref.watch(liverHistoryProvider);

  if (liverData.isEmpty) return null;

  final sortedDates = liverData.keys.toList()
    ..sort((a, b) => _parseDate(a).compareTo(_parseDate(b)));

  if (sortedDates.isEmpty) return null;

  final latestDate = sortedDates.last;
  final readings = liverData[latestDate]?['readings'] as List<dynamic>?;

  if (readings == null || readings.isEmpty) return null;

  return readings.last as Map<String, dynamic>;
});

// Provider for previous liver readings
final previousLiverReadingProvider = Provider<Map<String, dynamic>?>((ref) {
  final liverData = ref.watch(liverHistoryProvider);

  if (liverData.isEmpty) return null;

  final sortedDates = liverData.keys.toList()
    ..sort((a, b) => _parseDate(a).compareTo(_parseDate(b)));

  if (sortedDates.isEmpty) return null;

  // If there's only one date with multiple readings, use the second-to-last reading
  if (sortedDates.length == 1) {
    final readings = liverData[sortedDates[0]]?['readings'] as List<dynamic>?;
    if (readings != null && readings.length > 1) {
      return readings[readings.length - 2] as Map<String, dynamic>;
    }
    return null;
  }

  // Otherwise, use the last reading from the second-to-last date
  if (sortedDates.length > 1) {
    final previousDate = sortedDates[sortedDates.length - 2];
    final readings = liverData[previousDate]?['readings'] as List<dynamic>?;

    if (readings != null && readings.isNotEmpty) {
      return readings.last as Map<String, dynamic>;
    }
  }

  return null;
});

// Provider for liver health status
final liverHealthStatusProvider = Provider<String>((ref) {
  final latestReading = ref.watch(latestLiverReadingProvider);

  if (latestReading == null) return "Unknown";

  final ast = latestReading['ast'] as double?;
  final alt = latestReading['alt'] as double?;
  final alp = latestReading['alp'] as double?;
  final bilirubin = latestReading['bilirubin'] as double?;

  // If any of the key values are missing, return Unknown
  if (ast == null || alt == null || alp == null || bilirubin == null) {
    return "Unknown";
  }

  // Check if all values are within normal range
  bool isAstNormal = ast >= 5 && ast <= 40;
  bool isAltNormal = alt >= 7 && alt <= 56;
  bool isAlpNormal = alp >= 44 && alp <= 147;
  bool isBilirubinNormal = bilirubin >= 0.1 && bilirubin <= 1.2;

  if (isAstNormal && isAltNormal && isAlpNormal && isBilirubinNormal) {
    return "Normal";
  } else if (!isAstNormal && !isAltNormal && !isAlpNormal && !isBilirubinNormal) {
    return "Severe";
  } else {
    return "Moderate";
  }
});