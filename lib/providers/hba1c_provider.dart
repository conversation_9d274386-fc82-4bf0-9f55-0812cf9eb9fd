import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:healo/services/firestore_service.dart';
import 'package:healo/providers/user_provider.dart';
import 'dart:developer';

final hba1cHistoryProvider = StateNotifierProvider<Hba1cHistoryNotifier,
    Map<String, Map<String, dynamic>>>(
  (ref) => Hba1cHistoryNotifier(FirestoreService(), ref),
);

class Hba1cHistoryNotifier
    extends StateNotifier<Map<String, Map<String, dynamic>>> {
  final FirestoreService _firestoreService;
  final Ref _ref;

  Hba1cHistoryNotifier(this._firestoreService, this._ref) : super({}) {
    // Watch auth state to refresh data when user changes
    _ref.listen(authStateProvider, (previous, next) {
      next.when(
        data: (user) {
          if (user != null) {
            // User logged in, fetch data
            fetchHba1cHistory();
          } else {
            // User logged out, clear data
            state = {};
          }
        },
        loading: () {},
        error: (error, stack) {
          log("Auth state error in hba1c provider: $error");
        },
      );
    });
  }

  Future<void> addHba1cReading(
      String date, Map<String, dynamic> reading) async {
    try {
      await _firestoreService.addHba1cReading(date, reading);
      await fetchHba1cHistory();
    } catch (e) {
      log("Error adding HBA1c reading: $e");
    }
  }

  Future<void> fetchHba1cHistory() async {
    try {
      final data = await _firestoreService.fetchHba1cHistory();
      final hba1cHistory = Map<String, dynamic>.from(data['history'] ?? {});

      final Map<String, Map<String, dynamic>> formattedData = {};

      hba1cHistory.forEach((dateKey, dayData) {
        formattedData[dateKey] = {
          'readings': List<Map<String, dynamic>>.from(dayData['readings']),
        };
      });

      state = formattedData;
    } catch (e) {
      log("Error fetching HBA1c history: $e");
      state = {};
    }
  }
}

final latestHba1cReadingProvider =
    StateNotifierProvider<LatestHba1cReadingNotifier, double?>(
  (ref) => LatestHba1cReadingNotifier(FirestoreService(), ref),
);

class LatestHba1cReadingNotifier extends StateNotifier<double?> {
  final FirestoreService _firestoreService;
  final Ref _ref;

  LatestHba1cReadingNotifier(this._firestoreService, this._ref) : super(null) {
    // Watch auth state to refresh data when user changes
    _ref.listen(authStateProvider, (previous, next) {
      next.when(
        data: (user) {
          if (user != null) {
            // User logged in, fetch data
            fetchLatestReading();
          } else {
            // User logged out, clear data
            state = null;
          }
        },
        loading: () {},
        error: (error, stack) {
          log("Auth state error in latest hba1c reading provider: $error");
        },
      );
    });
  }

  Future<void> fetchLatestReading() async {
    try {
      final data = await _firestoreService.fetchHba1cHistory();
      final history = Map<String, dynamic>.from(data['history'] ?? {});

      if (history.isEmpty) {
        state = 0.0;
        return;
      }

      final sortedDates = history.keys.toList()
        ..sort((a, b) => _parseDate(b).compareTo(_parseDate(a)));

      final latestDate = sortedDates.first;
      final readings =
          List<Map<String, dynamic>>.from(history[latestDate]['readings']);

      if (readings.isNotEmpty) {
        final lastReading = readings.last;
        final hba1cValue = lastReading['hba1c'];

        if (hba1cValue is double) {
          state = hba1cValue;
        } else if (hba1cValue is int) {
          state = hba1cValue.toDouble();
        } else if (hba1cValue is String) {
          state = double.tryParse(hba1cValue);
        } else {
          state = null;
        }
      } else {
        state = null;
      }
    } catch (e) {
      log("Error fetching latest HBA1c reading: $e");
      state = null;
    }
  }

  DateTime _parseDate(String date) {
    try {
      final parts = date.split('-').map(int.parse).toList();
      return DateTime(parts[2], parts[1], parts[0]);
    } catch (e) {
      log("Invalid date format: $date");
      return DateTime(1970);
    }
  }
}

final latestHba1cDateProvider =
    StateNotifierProvider<LatestHba1cDateNotifier, String?>(
  (ref) => LatestHba1cDateNotifier(FirestoreService(), ref),
);

class LatestHba1cDateNotifier extends StateNotifier<String?> {
  final FirestoreService _firestoreService;
  final Ref _ref;

  LatestHba1cDateNotifier(this._firestoreService, this._ref) : super(null) {
    // Watch auth state to refresh data when user changes
    _ref.listen(authStateProvider, (previous, next) {
      next.when(
        data: (user) {
          if (user != null) {
            // User logged in, fetch data
            fetchLatestDate();
          } else {
            // User logged out, clear data
            state = null;
          }
        },
        loading: () {},
        error: (error, stack) {
          log("Auth state error in latest hba1c date provider: $error");
        },
      );
    });
  }

  Future<void> fetchLatestDate() async {
    try {
      final data = await _firestoreService.fetchHba1cHistory();
      final history = Map<String, dynamic>.from(data['history'] ?? {});

      if (history.isEmpty) {
        state = null;
        return;
      }

      final sortedDates = history.keys.toList()
        ..sort((a, b) => _parseDate(b).compareTo(_parseDate(a)));

      state = sortedDates.first;
    } catch (e) {
      log("Error fetching latest HBA1c date: $e");
      state = null;
    }
  }

  DateTime _parseDate(String date) {
    try {
      final parts = date.split('-').map(int.parse).toList();
      return DateTime(parts[2], parts[1], parts[0]);
    } catch (e) {
      log("Invalid date format: $date");
      return DateTime(1970);
    }
  }
}
