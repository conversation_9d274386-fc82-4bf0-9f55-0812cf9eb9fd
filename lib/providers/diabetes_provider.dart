import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:healo/services/firestore_service.dart';
import 'package:healo/providers/user_provider.dart';
import 'dart:developer';

final diabetesHistoryProvider = StateNotifierProvider<DiabetesHistoryNotifier,
    Map<String, Map<String, dynamic>>>(
  (ref) => DiabetesHistoryNotifier(FirestoreService(), ref),
);

class DiabetesHistoryNotifier
    extends StateNotifier<Map<String, Map<String, dynamic>>> {
  final FirestoreService _firestoreService;
  final Ref _ref;

  DiabetesHistoryNotifier(this._firestoreService, this._ref) : super({}) {
    // Watch auth state to refresh data when user changes
    _ref.listen(authStateProvider, (previous, next) {
      next.when(
        data: (user) {
          if (user != null) {
            // User logged in, fetch data
            fetchDiabetesHistory();
          } else {
            // User logged out, clear data
            state = {};
          }
        },
        loading: () {},
        error: (error, stack) {
          log("Auth state error in diabetes provider: $error");
        },
      );
    });
  }

  Future<void> addDiabetesReading(
      String date, Map<String, dynamic> reading) async {
    try {
      await _firestoreService.addDiabetesReading(date, reading);
      await fetchDiabetesHistory();
    } catch (e) {
      log("Error adding diabetes reading: $e");
    }
  }

  Future<void> addEstimatedHba1c(double hba1c) async {
    try {
      await _firestoreService.addEstimatedHaba1c(hba1c);
      await fetchDiabetesHistory();
    } catch (e) {
      log("Error adding estimated hba1c: $e");
    }
  }

  Future<void> fetchDiabetesHistory() async {
    try {
      final data = await _firestoreService.fetchDiabetesHistory();
      final diabetesHistory = Map<String, dynamic>.from(data['history'] ?? {});
      final Map<String, Map<String, dynamic>> formattedData = {};

      diabetesHistory.forEach((dateKey, dayData) {
        formattedData[dateKey] = {
          'readings': List<Map<String, dynamic>>.from(dayData['readings']),
        };
      });

      state = formattedData;
    } catch (e) {
      log("Error fetching diabetes history: $e");
      state = {};
    }
  }
}

final latestSugarReadingProvider =
    StateNotifierProvider<LatestSugarReadingNotifier, int?>(
  (ref) => LatestSugarReadingNotifier(FirestoreService(), ref),
);

class LatestSugarReadingNotifier extends StateNotifier<int?> {
  final FirestoreService _firestoreService;
  final Ref _ref;

  LatestSugarReadingNotifier(this._firestoreService, this._ref) : super(null) {
    // Watch auth state to refresh data when user changes
    _ref.listen(authStateProvider, (previous, next) {
      next.when(
        data: (user) {
          if (user != null) {
            // User logged in, fetch data
            fetchLatestReading();
          } else {
            // User logged out, clear data
            state = null;
          }
        },
        loading: () {},
        error: (error, stack) {
          log("Auth state error in latest sugar reading provider: $error");
        },
      );
    });
  }

  Future<void> fetchLatestReading() async {
    try {
      final data = await _firestoreService.fetchDiabetesHistory();
      final history = Map<String, dynamic>.from(data['history'] ?? {});

      if (history.isEmpty) {
        state = 0;
        return;
      }

      final sortedDates = history.keys.toList()
        ..sort((a, b) => _parseDate(b).compareTo(_parseDate(a)));

      final latestDate = sortedDates.first;
      final readings =
          List<Map<String, dynamic>>.from(history[latestDate]['readings']);

      if (readings.isNotEmpty) {
        final lastReading = readings.last;
        final sugarValue = lastReading['sugar'];

        if (sugarValue is int) {
          state = sugarValue;
        } else if (sugarValue is String) {
          state = int.tryParse(sugarValue);
        } else {
          state = null;
        }
      } else {
        state = null;
      }
    } catch (e) {
      log("Error fetching latest sugar reading: $e");
      state = null;
    }
  }

  DateTime _parseDate(String date) {
    try {
      final parts = date.split('-').map(int.parse).toList();
      return DateTime(parts[2], parts[1], parts[0]);
    } catch (e) {
      log("Invalid date format: $date");
      return DateTime(1970);
    }
  }
}

final estimatedHba1cProvider =
    StateNotifierProvider<EstimatedHba1cNotifier, double>(
  (ref) => EstimatedHba1cNotifier(FirestoreService(), ref),
);

class EstimatedHba1cNotifier extends StateNotifier<double> {
  final FirestoreService _firestoreService;
  final Ref _ref;

  EstimatedHba1cNotifier(this._firestoreService, this._ref) : super(0.0) {
    // Watch auth state to refresh data when user changes
    _ref.listen(authStateProvider, (previous, next) {
      next.when(
        data: (user) {
          if (user != null) {
            // User logged in, fetch data
            fetchEstimatedHba1c();
          } else {
            // User logged out, clear data
            state = 0.0;
          }
        },
        loading: () {},
        error: (error, stack) {
          log("Auth state error in estimated hba1c provider: $error");
        },
      );
    });
  }

  Future<void> fetchEstimatedHba1c() async {
    try {
      final data = await _firestoreService.fetchEstimatedHba1c();

      state = data;
    } catch (e) {
      log("Error fetching estimated hba1c: $e");
      state = 0.0;
    }
  }
}
