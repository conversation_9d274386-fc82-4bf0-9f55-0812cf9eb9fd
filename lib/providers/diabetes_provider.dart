import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:healo/services/firestore_service.dart';
import 'dart:developer';

final diabetesHistoryProvider = StateNotifierProvider<DiabetesHistoryNotifier,
    Map<String, Map<String, dynamic>>>(
  (ref) => DiabetesHistoryNotifier(FirestoreService()),
);

class DiabetesHistoryNotifier
    extends StateNotifier<Map<String, Map<String, dynamic>>> {
  final FirestoreService _firestoreService;
  DiabetesHistoryNotifier(this._firestoreService) : super({});

  Future<void> addDiabetesReading(
      String date, Map<String, dynamic> reading) async {
    try {
      await _firestoreService.addDiabetesReading(date, reading);
      await fetchDiabetesHistory();
    } catch (e) {
      log("Error adding diabetes reading: $e");
    }
  }

  Future<void> addEstimatedHba1c(double hba1c) async {
    try {
      await _firestoreService.addEstimatedHaba1c(hba1c);
      await fetchDiabetesHistory();
    } catch (e) {
      log("Error adding estimated hba1c: $e");
    }
  }

  Future<void> fetchDiabetesHistory() async {
    try {
      final data = await _firestoreService.fetchDiabetesHistory();
      final diabetesHistory = Map<String, dynamic>.from(data['history'] ?? {});
      final Map<String, Map<String, dynamic>> formattedData = {};

      diabetesHistory.forEach((dateKey, dayData) {
        formattedData[dateKey] = {
          'readings': List<Map<String, dynamic>>.from(dayData['readings']),
        };
      });

      state = formattedData;
    } catch (e) {
      log("Error fetching diabetes history: $e");
      state = {};
    }
  }
}

final latestSugarReadingProvider =
    StateNotifierProvider<LatestSugarReadingNotifier, int?>(
  (ref) => LatestSugarReadingNotifier(FirestoreService()),
);

class LatestSugarReadingNotifier extends StateNotifier<int?> {
  final FirestoreService _firestoreService;

  LatestSugarReadingNotifier(this._firestoreService) : super(null) {
    fetchLatestReading();
  }

  Future<void> fetchLatestReading() async {
    try {
      final data = await _firestoreService.fetchDiabetesHistory();
      final history = Map<String, dynamic>.from(data['history'] ?? {});

      if (history.isEmpty) {
        state = 0;
        return;
      }

      final sortedDates = history.keys.toList()
        ..sort((a, b) => _parseDate(b).compareTo(_parseDate(a)));

      final latestDate = sortedDates.first;
      final readings =
          List<Map<String, dynamic>>.from(history[latestDate]['readings']);

      if (readings.isNotEmpty) {
        final lastReading = readings.last;
        final sugarValue = lastReading['sugar'];

        if (sugarValue is int) {
          state = sugarValue;
        } else if (sugarValue is String) {
          state = int.tryParse(sugarValue);
        } else {
          state = null;
        }
      } else {
        state = null;
      }
    } catch (e) {
      log("Error fetching latest sugar reading: $e");
      state = null;
    }
  }

  DateTime _parseDate(String date) {
    try {
      final parts = date.split('-').map(int.parse).toList();
      return DateTime(parts[2], parts[1], parts[0]);
    } catch (e) {
      log("Invalid date format: $date");
      return DateTime(1970);
    }
  }
}

final estimatedHba1cProvider =
    StateNotifierProvider<EstimatedHba1cNotifier, double>(
  (ref) => EstimatedHba1cNotifier(FirestoreService()),
);

class EstimatedHba1cNotifier extends StateNotifier<double> {
  final FirestoreService _firestoreService;

  EstimatedHba1cNotifier(this._firestoreService) : super(0.0) {
    fetchEstimatedHba1c();
  }

  Future<void> fetchEstimatedHba1c() async {
    try {
      final data = await _firestoreService.fetchEstimatedHba1c();

      state = data;
    } catch (e) {
      log("Error fetching estimated hba1c: $e");
      state = 0.0;
    }
  }
}
