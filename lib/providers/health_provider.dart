import 'dart:developer';
import 'dart:io' show Platform;
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:healo/models/sleep_data_point.dart';
import 'package:healo/services/firestore_service.dart';
import 'package:health/health.dart';
import 'package:intl/intl.dart';

/// Health data model to store different types of health metrics
class HealthData {
  final int? steps;
  final double? distance;
  final double? calories;
  final double? heartRate;
  final double? sleepHours;

  HealthData({
    this.steps,
    this.distance,
    this.calories,
    this.heartRate,
    this.sleepHours,
  });

  /// Create a copy of the current object with some fields replaced
  HealthData copyWith({
    int? steps,
    double? distance,
    double? calories,
    double? heartRate,
    double? sleepHours,
  }) {
    return HealthData(
      steps: steps ?? this.steps,
      distance: distance ?? this.distance,
      calories: calories ?? this.calories,
      heartRate: heartRate ?? this.heartRate,
      sleepHours: sleepHours ?? this.sleepHours,
    );
  }
}

/// Abstract base class for platform-specific health implementations
abstract class PlatformHealthService {
  final Health health = Health();

  /// Initialize the health service
  Future<void> initialize();

  /// Check if health data is available on this device
  Future<bool> isHealthDataAvailable();

  /// Fetch all health data for the current day
  Future<HealthData> fetchHealthData();

  /// Fetch only steps data
  Future<int?> fetchStepsOnly();

  /// Fetch only heart rate data
  Future<double?> fetchHeartRateOnly();

  /// Fetch only calories data
  Future<double?> fetchCaloriesOnly();

  /// Fetch only sleep data
  Future<double?> fetchSleepData();

  /// Get daily step goal
  int getDailyStepGoal() => 8000;

  /// Get daily distance goal in kilometers
  double getDailyDistanceGoal() => 5.0;

  /// Get daily calorie goal
  double getDailyCalorieGoal() => 500.0;

  /// Calculate step progress percentage
  double getStepProgressPercentage(HealthData data) {
    final steps = data.steps ?? 0;
    final goal = getDailyStepGoal();
    return (steps / goal).clamp(0.0, 1.0);
  }

  /// Calculate distance progress percentage
  double getDistanceProgressPercentage(HealthData data) {
    final distance = (data.distance ?? 0) / 1000; // Convert to kilometers
    final goal = getDailyDistanceGoal();
    return (distance / goal).clamp(0.0, 1.0);
  }

  /// Calculate calorie progress percentage
  double getCalorieProgressPercentage(HealthData data) {
    final calories = data.calories ?? 0;
    final goal = getDailyCalorieGoal();
    return (calories / goal).clamp(0.0, 1.0);
  }

  /// Format distance for display (convert meters to kilometers)
  String getFormattedDistance(HealthData data) {
    final distance = data.distance ?? 0;
    return (distance / 1000).toStringAsFixed(1);
  }

  /// Factory method to create the appropriate platform implementation
  static PlatformHealthService create() {
    if (Platform.isIOS) {
      return IOSHealthService();
    } else {
      return AndroidHealthService();
    }
  }
}

/// iOS-specific implementation of health service
class IOSHealthService extends PlatformHealthService {
  /// List of health data types to fetch for iOS
  List<HealthDataType> get _dataTypes => [
        HealthDataType.STEPS,
        HealthDataType.DISTANCE_WALKING_RUNNING,
        HealthDataType.ACTIVE_ENERGY_BURNED,
        HealthDataType.HEART_RATE,
        ..._sleepDataTypes,
      ];

  /// Get only sleep-related data types
  List<HealthDataType> get _sleepDataTypes => [
        HealthDataType.SLEEP_ASLEEP,
        HealthDataType.SLEEP_AWAKE,
        HealthDataType.SLEEP_IN_BED,
        HealthDataType.SLEEP_DEEP,
        HealthDataType.SLEEP_REM,
        HealthDataType.SLEEP_LIGHT,
      ];

  @override
  Future<void> initialize() async {
    try {
      await health.configure();
      log("iOS Health service initialized");
    } catch (e) {
      log("Error initializing iOS Health service: $e");
    }
  }

  @override
  Future<bool> isHealthDataAvailable() async {
    try {
      await health.configure();
      return await health.hasPermissions([HealthDataType.STEPS]) ?? false;
    } catch (e) {
      log("Error checking iOS Health data availability: $e");
      return false;
    }
  }

  @override
  Future<HealthData> fetchHealthData() async {
    try {
      // Configure the health plugin
      await health.configure();

      final DateTime now = DateTime.now();
      final DateTime startTime = DateTime(now.year, now.month, now.day);

      log("Fetching iOS health data for today");

      // For iOS, we need to specify READ permissions
      List<HealthDataAccess> permissions =
          List.filled(_dataTypes.length, HealthDataAccess.READ);

      // Request authorization for the health data types
      final bool authorized = await health.requestAuthorization(
        _dataTypes,
        permissions: permissions,
      );

      if (!authorized) {
        log("iOS Health data authorization not granted");
        return HealthData();
      }

      // Fetch health data from the device
      List<HealthDataPoint> healthData = [];

      // On iOS, fetch each data type separately for better reliability
      for (HealthDataType type in _dataTypes) {
        try {
          List<HealthDataPoint> typeData = await health.getHealthDataFromTypes(
            startTime: startTime,
            endTime: now,
            types: [type],
          );
          healthData.addAll(typeData);
        } catch (e) {
          log("Error fetching iOS data for type $type: $e");
        }
      }

      // Remove duplicates
      if (healthData.isNotEmpty) {
        healthData = health.removeDuplicates(healthData);
      }

      // Process the health data
      int? steps;
      double? distance;
      double? calories;
      double? heartRate;
      double? sleepHours;

      // Try to get total steps directly (more reliable on iOS)
      try {
        int? totalSteps = await health.getTotalStepsInInterval(startTime, now);
        steps = totalSteps;
      } catch (e) {
        log("Error fetching iOS total steps: $e");
      }

      // Process individual data points
      for (var dataPoint in healthData) {
        if (dataPoint.value is NumericHealthValue) {
          final numericValue =
              (dataPoint.value as NumericHealthValue).numericValue;

          // Process data based on type
          if (dataPoint.type == HealthDataType.STEPS && steps == null) {
            steps = numericValue.round();
          } else if (dataPoint.type ==
              HealthDataType.DISTANCE_WALKING_RUNNING) {
            distance = (distance ?? 0) + numericValue.toDouble();
          } else if (dataPoint.type == HealthDataType.ACTIVE_ENERGY_BURNED) {
            calories = (calories ?? 0) + numericValue.toDouble();
          } else if (dataPoint.type == HealthDataType.HEART_RATE) {
            // For heart rate, take the first reading we find
            heartRate ??= numericValue.toDouble();
          } else if (_sleepDataTypes.contains(dataPoint.type)) {
            // Convert sleep time to hours (sleep is stored in minutes)
            sleepHours = (sleepHours ?? 0) + (numericValue.toDouble() / 60);
          }
        }
      }

      // Return the new health data
      return HealthData(
        steps: steps,
        distance: distance,
        calories: calories,
        heartRate: heartRate,
        sleepHours: sleepHours,
      );
    } catch (e) {
      log("Error fetching iOS health data: $e");
      return HealthData();
    }
  }

  @override
  Future<double?> fetchCaloriesOnly() async {
    try {
      await health.configure();

      final DateTime now = DateTime.now();
      final DateTime startTime = DateTime(now.year, now.month, now.day);

      // Set up permissions for iOS
      List<HealthDataAccess> permissions = [HealthDataAccess.READ];

      // Request authorization
      final bool authorized = await health.requestAuthorization(
        [HealthDataType.ACTIVE_ENERGY_BURNED],
        permissions: permissions,
      );

      if (!authorized) return null;

      // Fetch calories data points
      List<HealthDataPoint> caloriesData = await health.getHealthDataFromTypes(
        startTime: startTime,
        endTime: now,
        types: [HealthDataType.ACTIVE_ENERGY_BURNED],
      );

      if (caloriesData.isEmpty) return null;

      // Process calories data
      double totalCalories = 0;
      for (var dataPoint in caloriesData) {
        if (dataPoint.value is NumericHealthValue) {
          final numericValue =
              (dataPoint.value as NumericHealthValue).numericValue;
          totalCalories += numericValue.toDouble();
        }
      }

      return totalCalories > 0 ? totalCalories : null;
    } catch (e) {
      log("Error in iOS fetchCaloriesOnly: $e");
      return null;
    }
  }

  @override
  Future<double?> fetchHeartRateOnly() async {
    try {
      await health.configure();

      final DateTime now = DateTime.now();
      final DateTime startTime = DateTime(now.year, now.month, now.day);

      // Set up permissions for iOS
      List<HealthDataAccess> permissions = [HealthDataAccess.READ];

      // Request authorization
      final bool authorized = await health.requestAuthorization(
        [HealthDataType.HEART_RATE],
        permissions: permissions,
      );

      if (!authorized) return null;

      // Fetch heart rate data points
      List<HealthDataPoint> heartRateData = await health.getHealthDataFromTypes(
        startTime: startTime,
        endTime: now,
        types: [HealthDataType.HEART_RATE],
      );

      if (heartRateData.isEmpty) return null;

      // Sort data points by date to get the most recent one
      heartRateData.sort((a, b) => b.dateTo.compareTo(a.dateTo));

      // Get the most recent heart rate value
      for (var dataPoint in heartRateData) {
        if (dataPoint.value is NumericHealthValue) {
          final numericValue =
              (dataPoint.value as NumericHealthValue).numericValue;
          return numericValue.toDouble();
        }
      }

      return null;
    } catch (e) {
      log("Error in iOS fetchHeartRateOnly: $e");
      return null;
    }
  }

  @override
  Future<int?> fetchStepsOnly() async {
    try {
      await health.configure();

      final DateTime now = DateTime.now();
      final DateTime startTime = DateTime(now.year, now.month, now.day);

      // Set up permissions for iOS
      List<HealthDataAccess> permissions = [HealthDataAccess.READ];

      // Request authorization
      final bool authorized = await health.requestAuthorization(
        [HealthDataType.STEPS],
        permissions: permissions,
      );

      if (!authorized) return null;

      // Try to get total steps directly (most reliable method on iOS)
      try {
        int? totalSteps = await health.getTotalStepsInInterval(startTime, now);
        if (totalSteps != null && totalSteps > 0) {
          return totalSteps;
        }
      } catch (e) {
        log("Error fetching iOS total steps directly: $e");
      }

      // If direct method failed, try fetching step data points
      List<HealthDataPoint> stepData = await health.getHealthDataFromTypes(
        startTime: startTime,
        endTime: now,
        types: [HealthDataType.STEPS],
      );

      // Process step data
      int steps = 0;
      for (var dataPoint in stepData) {
        if (dataPoint.value is NumericHealthValue) {
          final numericValue =
              (dataPoint.value as NumericHealthValue).numericValue;
          steps += numericValue.round();
        }
      }

      return steps > 0 ? steps : null;
    } catch (e) {
      log("Error in iOS fetchStepsOnly: $e");
      return null;
    }
  }

  @override
  Future<double?> fetchSleepData() async {
    try {
      await health.configure();

      final DateTime now = DateTime.now();
      // Fetch sleep data for the last 7 days instead of just today
      final DateTime startTime = DateTime(now.year, now.month, now.day)
          .subtract(const Duration(days: 7));

      // For iOS, we need to specify READ permissions
      List<HealthDataAccess> permissions =
          List.filled(_sleepDataTypes.length, HealthDataAccess.READ);

      // Request authorization for sleep data types
      final bool authorized = await health.requestAuthorization(
        _sleepDataTypes,
        permissions: permissions,
      );

      if (!authorized) {
        return null;
      }

      // First try to use SLEEP_ASLEEP for total sleep time
      // This avoids double-counting sleep stages which are subsets of SLEEP_ASLEEP
      double totalSleepHours = 0;
      bool dataFound = false;
      Map<HealthDataType, List<HealthDataPoint>> allSleepData = {};

      // Get today's date at midnight for filtering
      final DateTime todayMidnight = DateTime(now.year, now.month, now.day);
      final DateTime yesterdayMidnight =
          todayMidnight.subtract(const Duration(days: 1));

      // First, collect all sleep data types
      for (HealthDataType type in _sleepDataTypes) {
        try {
          List<HealthDataPoint> sleepData = await health.getHealthDataFromTypes(
            startTime: startTime,
            endTime: now,
            types: [type],
          );

          // Filter to only include the most recent night's sleep (from yesterday evening to today morning)
          List<HealthDataPoint> filteredData = sleepData.where((dataPoint) {
            // Include data points that end today or start after yesterday midnight
            return dataPoint.dateTo.isAfter(yesterdayMidnight) &&
                dataPoint.dateTo
                    .isBefore(todayMidnight.add(const Duration(hours: 12)));
          }).toList();

          allSleepData[type] = filteredData;
        } catch (e) {
          log("Error fetching iOS sleep data for type $type: $e");
        }
      }

      // First try to use SLEEP_ASLEEP (preferred method)
      if (allSleepData.containsKey(HealthDataType.SLEEP_ASLEEP) &&
          allSleepData[HealthDataType.SLEEP_ASLEEP]!.isNotEmpty) {
        for (var dataPoint in allSleepData[HealthDataType.SLEEP_ASLEEP]!) {
          if (dataPoint.value is NumericHealthValue) {
            final numericValue =
                (dataPoint.value as NumericHealthValue).numericValue;
            // Convert minutes to hours
            double hours = numericValue.toDouble() / 60.0;
            totalSleepHours += hours;
            dataFound = true;
          }
        }
      }
      // If no SLEEP_ASLEEP data, try to use SLEEP_IN_BED or other types
      else if (allSleepData.isNotEmpty) {
        // Try SLEEP_IN_BED first as it's the most reliable alternative
        HealthDataType typeToUse = HealthDataType.SLEEP_IN_BED;

        if (allSleepData.containsKey(typeToUse) &&
            allSleepData[typeToUse]!.isNotEmpty) {
          for (var dataPoint in allSleepData[typeToUse]!) {
            if (dataPoint.value is NumericHealthValue) {
              final numericValue =
                  (dataPoint.value as NumericHealthValue).numericValue;
              // Convert minutes to hours
              double hours = numericValue.toDouble() / 60.0;
              totalSleepHours += hours;
              dataFound = true;
            }
          }
        }
        // If still no data, try any other sleep type that has data
        else {
          for (var entry in allSleepData.entries) {
            if (entry.value.isNotEmpty) {
              typeToUse = entry.key;

              for (var dataPoint in entry.value) {
                if (dataPoint.value is NumericHealthValue) {
                  final numericValue =
                      (dataPoint.value as NumericHealthValue).numericValue;
                  // Convert minutes to hours
                  double hours = numericValue.toDouble() / 60.0;
                  totalSleepHours += hours;
                  dataFound = true;
                }
              }

              // Only use one type to avoid double-counting
              break;
            }
          }
        }
      }
      return dataFound ? totalSleepHours : null;
    } catch (e) {
      return null;
    }
  }
}

/// Android-specific implementation of health service
class AndroidHealthService extends PlatformHealthService {
  /// List of health data types to fetch for Android
  List<HealthDataType> get _dataTypes => [
        HealthDataType.STEPS,
        HealthDataType.DISTANCE_DELTA,
        HealthDataType.ACTIVE_ENERGY_BURNED,
        HealthDataType.HEART_RATE,
        ..._sleepDataTypes,
      ];

  /// Get only sleep-related data types
  List<HealthDataType> get _sleepDataTypes => [
        HealthDataType.SLEEP_ASLEEP,
        HealthDataType.SLEEP_AWAKE,
        HealthDataType.SLEEP_IN_BED,
        HealthDataType.SLEEP_DEEP,
        HealthDataType.SLEEP_REM,
        HealthDataType.SLEEP_LIGHT,
      ];

  @override
  Future<void> initialize() async {
    try {
      await health.configure();
      log("Android Health service initialized");
    } catch (e) {
      log("Error initializing Android Health service: $e");
    }
  }

  @override
  Future<bool> isHealthDataAvailable() async {
    try {
      await health.configure();
      return await health.hasPermissions([HealthDataType.STEPS]) ?? false;
    } catch (e) {
      log("Error checking Android Health data availability: $e");
      return false;
    }
  }

  @override
  Future<HealthData> fetchHealthData() async {
    try {
      // Configure the health plugin
      await health.configure();

      final DateTime now = DateTime.now();
      final DateTime startTime = DateTime(now.year, now.month, now.day);

      log("Fetching Android health data for today");

      // Request authorization for the health data types
      final bool authorized = await health.requestAuthorization(_dataTypes);

      if (!authorized) {
        log("Android Health data authorization not granted");
        return HealthData();
      }

      // Fetch health data from the device
      List<HealthDataPoint> healthData = [];

      // On Android, fetch all types at once
      try {
        healthData = await health.getHealthDataFromTypes(
          startTime: startTime,
          endTime: now,
          types: _dataTypes,
        );
      } catch (e) {
        log("Error fetching Android health data points: $e");
      }

      // Remove duplicates
      if (healthData.isNotEmpty) {
        healthData = health.removeDuplicates(healthData);
      }

      // Process the health data
      int? steps;
      double? distance;
      double? calories;
      double? heartRate;
      double? sleepHours;

      // Process individual data points
      for (var dataPoint in healthData) {
        if (dataPoint.value is NumericHealthValue) {
          final numericValue =
              (dataPoint.value as NumericHealthValue).numericValue;

          // Process data based on type
          if (dataPoint.type == HealthDataType.STEPS) {
            steps = (steps ?? 0) + numericValue.round();
          } else if (dataPoint.type == HealthDataType.DISTANCE_DELTA) {
            distance = (distance ?? 0) + numericValue.toDouble();
          } else if (dataPoint.type == HealthDataType.ACTIVE_ENERGY_BURNED) {
            calories = (calories ?? 0) + numericValue.toDouble();
          } else if (dataPoint.type == HealthDataType.HEART_RATE) {
            // For heart rate, take the first reading we find
            heartRate ??= numericValue.toDouble();
          } else if (_sleepDataTypes.contains(dataPoint.type)) {
            // Convert sleep time to hours (sleep is stored in minutes)
            sleepHours = (sleepHours ?? 0) + (numericValue.toDouble() / 60);
          }
        }
      }

      // Return the new health data
      return HealthData(
        steps: steps,
        distance: distance,
        calories: calories,
        heartRate: heartRate,
        sleepHours: sleepHours,
      );
    } catch (e) {
      log("Error fetching Android health data: $e");
      return HealthData();
    }
  }

  @override
  Future<double?> fetchCaloriesOnly() async {
    try {
      await health.configure();

      final DateTime now = DateTime.now();
      final DateTime startTime = DateTime(now.year, now.month, now.day);

      // Request authorization
      final bool authorized = await health.requestAuthorization(
        [HealthDataType.ACTIVE_ENERGY_BURNED],
      );

      if (!authorized) return null;

      // Fetch calories data points
      List<HealthDataPoint> caloriesData = await health.getHealthDataFromTypes(
        startTime: startTime,
        endTime: now,
        types: [HealthDataType.ACTIVE_ENERGY_BURNED],
      );

      if (caloriesData.isEmpty) return null;

      // Process calories data
      double totalCalories = 0;
      for (var dataPoint in caloriesData) {
        if (dataPoint.value is NumericHealthValue) {
          final numericValue =
              (dataPoint.value as NumericHealthValue).numericValue;
          totalCalories += numericValue.toDouble();
        }
      }

      return totalCalories > 0 ? totalCalories : null;
    } catch (e) {
      log("Error in Android fetchCaloriesOnly: $e");
      return null;
    }
  }

  @override
  Future<double?> fetchHeartRateOnly() async {
    try {
      await health.configure();

      final DateTime now = DateTime.now();
      final DateTime startTime = DateTime(now.year, now.month, now.day);

      // Request authorization
      final bool authorized = await health.requestAuthorization(
        [HealthDataType.HEART_RATE],
      );

      if (!authorized) return null;

      // Fetch heart rate data points
      List<HealthDataPoint> heartRateData = await health.getHealthDataFromTypes(
        startTime: startTime,
        endTime: now,
        types: [HealthDataType.HEART_RATE],
      );

      if (heartRateData.isEmpty) return null;

      // Sort data points by date to get the most recent one
      heartRateData.sort((a, b) => b.dateTo.compareTo(a.dateTo));

      // Get the most recent heart rate value
      for (var dataPoint in heartRateData) {
        if (dataPoint.value is NumericHealthValue) {
          final numericValue =
              (dataPoint.value as NumericHealthValue).numericValue;
          return numericValue.toDouble();
        }
      }

      return null;
    } catch (e) {
      log("Error in Android fetchHeartRateOnly: $e");
      return null;
    }
  }

  @override
  Future<int?> fetchStepsOnly() async {
    try {
      await health.configure();

      final DateTime now = DateTime.now();
      final DateTime startTime = DateTime(now.year, now.month, now.day);

      // Request authorization
      final bool authorized = await health.requestAuthorization(
        [HealthDataType.STEPS],
      );

      if (!authorized) return null;

      // Fetch step data points
      List<HealthDataPoint> stepData = await health.getHealthDataFromTypes(
        startTime: startTime,
        endTime: now,
        types: [HealthDataType.STEPS],
      );

      // Process step data
      int steps = 0;
      for (var dataPoint in stepData) {
        if (dataPoint.value is NumericHealthValue) {
          final numericValue =
              (dataPoint.value as NumericHealthValue).numericValue;
          steps += numericValue.round();
        }
      }

      return steps > 0 ? steps : null;
    } catch (e) {
      log("Error in Android fetchStepsOnly: $e");
      return null;
    }
  }

  @override
  Future<double?> fetchSleepData() async {
    try {
      await health.configure();

      final DateTime now = DateTime.now();
      // Fetch sleep data for the last 7 days instead of just today
      final DateTime startTime = DateTime(now.year, now.month, now.day)
          .subtract(const Duration(days: 7));

      // Request authorization for sleep data types
      final bool authorized =
          await health.requestAuthorization(_sleepDataTypes);

      if (!authorized) {
        return null;
      }

      // First try to use SLEEP_ASLEEP for total sleep time
      // This avoids double-counting sleep stages which are subsets of SLEEP_ASLEEP
      double totalSleepHours = 0;
      bool dataFound = false;
      Map<HealthDataType, List<HealthDataPoint>> allSleepData = {};

      // Get today's date at midnight for filtering
      final DateTime todayMidnight = DateTime(now.year, now.month, now.day);
      final DateTime yesterdayMidnight =
          todayMidnight.subtract(const Duration(days: 1));

      // First, collect all sleep data types
      for (HealthDataType type in _sleepDataTypes) {
        try {
          List<HealthDataPoint> sleepData = await health.getHealthDataFromTypes(
            startTime: startTime,
            endTime: now,
            types: [type],
          );

          // Filter to only include the most recent night's sleep (from yesterday evening to today morning)
          List<HealthDataPoint> filteredData = sleepData.where((dataPoint) {
            // Include data points that end today or start after yesterday midnight
            return dataPoint.dateTo.isAfter(yesterdayMidnight) &&
                dataPoint.dateTo
                    .isBefore(todayMidnight.add(const Duration(hours: 12)));
          }).toList();

          allSleepData[type] = filteredData;
        } catch (e) {
          log("HEALO_DEBUG: Error fetching Android sleep data for type $type: $e");
        }
      }

      // First try to use SLEEP_ASLEEP (preferred method)
      if (allSleepData.containsKey(HealthDataType.SLEEP_ASLEEP) &&
          allSleepData[HealthDataType.SLEEP_ASLEEP]!.isNotEmpty) {
        for (var dataPoint in allSleepData[HealthDataType.SLEEP_ASLEEP]!) {
          if (dataPoint.value is NumericHealthValue) {
            final numericValue =
                (dataPoint.value as NumericHealthValue).numericValue;
            // Convert minutes to hours
            double hours = numericValue.toDouble() / 60.0;
            totalSleepHours += hours;
            dataFound = true;
          }
        }
      }
      // If no SLEEP_ASLEEP data, try to use SLEEP_IN_BED or other types
      else if (allSleepData.isNotEmpty) {
        // Try SLEEP_IN_BED first as it's the most reliable alternative
        HealthDataType typeToUse = HealthDataType.SLEEP_IN_BED;

        if (allSleepData.containsKey(typeToUse) &&
            allSleepData[typeToUse]!.isNotEmpty) {
          for (var dataPoint in allSleepData[typeToUse]!) {
            if (dataPoint.value is NumericHealthValue) {
              final numericValue =
                  (dataPoint.value as NumericHealthValue).numericValue;
              // Convert minutes to hours
              double hours = numericValue.toDouble() / 60.0;
              totalSleepHours += hours;
              dataFound = true;
            }
          }
        }
        // If still no data, try any other sleep type that has data
        else {
          for (var entry in allSleepData.entries) {
            if (entry.value.isNotEmpty) {
              typeToUse = entry.key;

              for (var dataPoint in entry.value) {
                if (dataPoint.value is NumericHealthValue) {
                  final numericValue =
                      (dataPoint.value as NumericHealthValue).numericValue;
                  // Convert minutes to hours
                  double hours = numericValue.toDouble() / 60.0;
                  totalSleepHours += hours;
                  dataFound = true;
                }
              }

              // Only use one type to avoid double-counting
              break;
            }
          }
        }
      }

      return dataFound ? totalSleepHours : null;
    } catch (e) {
      return null;
    }
  }
}

/// Provider for health data
class HealthDataNotifier extends StateNotifier<HealthData> {
  final PlatformHealthService _platformService;
  Map<String, dynamic> _healthHistory = {};

  HealthDataNotifier(Ref ref)
      : _platformService = PlatformHealthService.create(),
        super(HealthData()) {
    _initialize();
  }

  Future<void> _initialize() async {
    await _platformService.initialize();
  }

  /// Refresh all health data
  Future<void> refreshHealthData() async {
    // Clear any cached data
    state = HealthData();
    bool deviceDataFetched = false;

    try {
      // Fetch specialized data first for accuracy
      int? accurateSteps = await _platformService.fetchStepsOnly();
      double? accurateHeartRate = await _platformService.fetchHeartRateOnly();
      double? accurateCalories = await _platformService.fetchCaloriesOnly();

      // Update state with specialized data
      if (accurateSteps != null) {
        state = state.copyWith(steps: accurateSteps);
      }

      if (accurateHeartRate != null) {
        state = state.copyWith(heartRate: accurateHeartRate);
      }

      if (accurateCalories != null) {
        state = state.copyWith(calories: accurateCalories);
      }

      // Fetch general health data
      final healthData = await _platformService.fetchHealthData();

      // Merge with existing state, prioritizing specialized data
      state = HealthData(
        steps: state.steps ?? healthData.steps,
        distance: healthData.distance,
        calories: state.calories ?? healthData.calories,
        heartRate: state.heartRate ?? healthData.heartRate,
        sleepHours: healthData.sleepHours,
      );

      // Always try to fetch specialized sleep data
      double? sleepHours = await _platformService.fetchSleepData();
      if (sleepHours != null && sleepHours > 0) {
        state = state.copyWith(sleepHours: sleepHours);
      }

      // Check if we got any meaningful data from the device
      deviceDataFetched = (state.steps != null && state.steps! > 0) ||
          (state.distance != null && state.distance! > 0) ||
          (state.calories != null && state.calories! > 0) ||
          (state.heartRate != null && state.heartRate! > 0) ||
          (state.sleepHours != null && state.sleepHours! > 0);
    } catch (e) {
      log("Error fetching health data from device: $e");
      deviceDataFetched = false;
    }

    // If device data fetch failed or returned no meaningful data, try Firestore
    if (!deviceDataFetched) {
      try {
        final firestoreService = FirestoreService();
        final healthHistory = await firestoreService.fetchHealthHistory();

        // Store the entire health history for chart data
        _healthHistory = healthHistory;

        if (healthHistory.containsKey('latest')) {
          final latestData = healthHistory['latest'];

          // Check if the latest data is from today
          final int? timestamp = latestData['timestamp'] as int?;
          final bool isFromToday = timestamp != null && _isFromToday(timestamp);

          if (isFromToday) {
            // Update state with latest data from Firestore only if it's from today
            state = HealthData(
              steps: latestData['steps'],
              distance: latestData['distance'],
              calories: latestData['calories'],
              heartRate: latestData['heartRate'],
              sleepHours: latestData['sleepHours'],
            );

            log("Using latest health data from Firestore (from today)");
            return; // Exit early as we've restored from Firestore
          } else {
            log("Latest health data from Firestore is not from today, not using it");
          }
        }
      } catch (e) {
        log("Error fetching health data from Firestore: $e");
      }
    }

    // Only upload to Firebase if we got new data from the device
    if (deviceDataFetched) {
      try {
        final firestoreService = FirestoreService();
        final now = DateTime.now();
        final formattedDate = "${now.day}-${now.month}-${now.year}";

        final readingData = {
          'day': DateFormat('EEEE').format(now),
          'steps': state.steps,
          'distance': state.distance,
          'calories': state.calories,
          'heartRate': state.heartRate,
          'sleepHours': state.sleepHours,
          'timestamp': DateTime.now().millisecondsSinceEpoch,
        };

        await firestoreService.addHealthDataReading(formattedDate, readingData);
      } catch (e) {
        log("Error uploading health data to Firebase: $e");
      }
    }
  }

  /// Check if health data is available on this device
  Future<bool> isHealthDataAvailable() {
    return _platformService.isHealthDataAvailable();
  }

  /// Update only the steps count
  void updateStepsOnly(int steps) {
    state = state.copyWith(steps: steps);
  }

  /// Update only the heart rate
  void updateHeartRateOnly(double heartRate) {
    state = state.copyWith(heartRate: heartRate);
  }

  /// Update only the calories
  void updateCaloriesOnly(double calories) {
    state = state.copyWith(calories: calories);
  }

  /// Get daily step goal
  int getDailyStepGoal() => _platformService.getDailyStepGoal();

  /// Get daily distance goal in kilometers
  double getDailyDistanceGoal() => _platformService.getDailyDistanceGoal();

  /// Get daily calorie goal
  double getDailyCalorieGoal() => _platformService.getDailyCalorieGoal();

  /// Calculate step progress percentage
  double getStepProgressPercentage() {
    return _platformService.getStepProgressPercentage(state);
  }

  /// Calculate distance progress percentage
  double getDistanceProgressPercentage() {
    return _platformService.getDistanceProgressPercentage(state);
  }

  /// Calculate calorie progress percentage
  double getCalorieProgressPercentage() {
    return _platformService.getCalorieProgressPercentage(state);
  }

  /// Format distance for display (convert meters to kilometers)
  String getFormattedDistance() {
    return _platformService.getFormattedDistance(state);
  }

  /// Initialize health data from Firestore on app start
  Future<void> initializeFromFirestore() async {
    try {
      final firestoreService = FirestoreService();
      final healthHistory = await firestoreService.fetchHealthHistory();

      // Store the entire health history for chart data
      _healthHistory = healthHistory;

      if (healthHistory.containsKey('latest')) {
        final latestData = healthHistory['latest'];

        // Check if the latest data is from today
        final int? timestamp = latestData['timestamp'] as int?;
        final bool isFromToday = timestamp != null && _isFromToday(timestamp);

        if (isFromToday) {
          // Update state with latest data from Firestore only if it's from today
          state = HealthData(
            steps: latestData['steps'],
            distance: latestData['distance'],
            calories: latestData['calories'],
            heartRate: latestData['heartRate'],
            sleepHours: latestData['sleepHours'],
          );

          log("Initialized health data from Firestore (from today)");
        } else {
          log("Latest health data from Firestore is not from today, not using it");
        }
      }
    } catch (e) {
      log("Error initializing health data from Firestore: $e");
    }
  }

  /// Get historical health data for charts
  Map<String, dynamic> getHealthHistory() {
    return _healthHistory;
  }

  /// Fetch historical health data from Firestore
  Future<Map<String, dynamic>> fetchHealthHistory() async {
    try {
      final firestoreService = FirestoreService();
      final healthHistory = await firestoreService.fetchHealthHistory();

      // Store the health history for later use
      _healthHistory = healthHistory;

      return healthHistory;
    } catch (e) {
      log("Error fetching health history from Firestore: $e");
      return {};
    }
  }

  /// Fetch historical sleep data directly from Health Connect/Apple Health
  Future<List<SleepDataPoint>> fetchHistoricalSleepData(int days) async {
    List<SleepDataPoint> sleepData = [];

    try {
      // Configure the health plugin
      await _platformService.health.configure();

      final DateTime now = DateTime.now();
      final DateTime startTime =
          DateTime(now.year, now.month, now.day).subtract(Duration(days: days));

      // Get sleep data types from the platform service
      final sleepDataTypes = Platform.isIOS
          ? ((_platformService as IOSHealthService)._sleepDataTypes)
          : ((_platformService as AndroidHealthService)._sleepDataTypes);

      // Request authorization
      final bool authorized =
          await _platformService.health.requestAuthorization(
        sleepDataTypes,
        permissions: List.filled(sleepDataTypes.length, HealthDataAccess.READ),
      );

      if (!authorized) {
        return [];
      }

      // Collect all sleep data by type
      Map<DateTime, double> dailySleepHours = {};

      // First try SLEEP_ASLEEP
      List<HealthDataPoint> sleepAsleepData =
          await _platformService.health.getHealthDataFromTypes(
        startTime: startTime,
        endTime: now,
        types: [HealthDataType.SLEEP_ASLEEP],
      );

      // If we have SLEEP_ASLEEP data, use it
      if (sleepAsleepData.isNotEmpty) {
        _processSleepDataPoints(sleepAsleepData, dailySleepHours);
      }
      // Otherwise try SLEEP_IN_BED
      else {
        List<HealthDataPoint> sleepInBedData =
            await _platformService.health.getHealthDataFromTypes(
          startTime: startTime,
          endTime: now,
          types: [HealthDataType.SLEEP_IN_BED],
        );

        if (sleepInBedData.isNotEmpty) {
          _processSleepDataPoints(sleepInBedData, dailySleepHours);
        }
        // If still no data, try other sleep types
        else {
          for (HealthDataType type in sleepDataTypes) {
            if (type == HealthDataType.SLEEP_ASLEEP ||
                type == HealthDataType.SLEEP_IN_BED) {
              continue;
            }

            List<HealthDataPoint> otherSleepData =
                await _platformService.health.getHealthDataFromTypes(
              startTime: startTime,
              endTime: now,
              types: [type],
            );

            if (otherSleepData.isNotEmpty) {
              _processSleepDataPoints(otherSleepData, dailySleepHours);
              break; // Only use one type to avoid double-counting
            }
          }
        }
      }

      // Convert the map to a list of SleepDataPoint objects
      dailySleepHours.forEach((date, hours) {
        sleepData.add(SleepDataPoint(date, hours));
      });

      // Sort by date
      sleepData.sort((a, b) => a.date.compareTo(b.date));
    } catch (e) {
      log("HEALO_DEBUG: Error fetching historical sleep data: $e");
    }

    return sleepData;
  }

  // Helper method to process sleep data points and aggregate by day
  void _processSleepDataPoints(
      List<HealthDataPoint> dataPoints, Map<DateTime, double> dailySleepHours) {
    for (var dataPoint in dataPoints) {
      if (dataPoint.value is NumericHealthValue) {
        final numericValue =
            (dataPoint.value as NumericHealthValue).numericValue;
        // Convert minutes to hours
        double hours = numericValue.toDouble() / 60.0;

        // Use the end date (dateTo) to determine which day this sleep belongs to
        final DateTime day = DateTime(
          dataPoint.dateTo.year,
          dataPoint.dateTo.month,
          dataPoint.dateTo.day,
        );

        // Add to the daily total
        dailySleepHours[day] = (dailySleepHours[day] ?? 0) + hours;
      }
    }
  }

  /// Helper method to check if a timestamp is from today
  bool _isFromToday(int timestamp) {
    final DateTime now = DateTime.now();
    final DateTime timestampDate =
        DateTime.fromMillisecondsSinceEpoch(timestamp);

    return now.year == timestampDate.year &&
        now.month == timestampDate.month &&
        now.day == timestampDate.day;
  }
}

/// Provider for health data
final healthDataProvider =
    StateNotifierProvider<HealthDataNotifier, HealthData>(
  (ref) => HealthDataNotifier(ref),
);
