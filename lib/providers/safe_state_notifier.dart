import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'dart:developer';

/// A mixin that provides safe state updates for StateNotifiers
/// This prevents "Bad state: Tried to use StateNotifier after dispose" errors
mixin SafeStateNotifier<T> on StateNotifier<T> {
  
  /// Safely update the state only if the notifier is still mounted
  void safeSetState(T newState) {
    if (mounted) {
      state = newState;
    }
  }

  /// Safely update the state with a function only if the notifier is still mounted
  void safeUpdateState(T Function(T current) updater) {
    if (mounted) {
      state = updater(state);
    }
  }

  /// Execute an async operation and safely update state
  Future<void> safeAsyncOperation(
    Future<T> Function() operation, {
    T? fallbackState,
    String? errorMessage,
  }) async {
    try {
      final result = await operation();
      
      // Check if still mounted before updating state
      if (mounted) {
        state = result;
      }
    } catch (e) {
      if (errorMessage != null) {
        log("$errorMessage: $e");
      }
      
      // Only update state if still mounted and fallback is provided
      if (mounted && fallbackState != null) {
        state = fallbackState;
      }
    }
  }

  /// Execute an async operation with custom state handling
  Future<void> safeAsyncOperationWithHandler(
    Future<void> Function() operation, {
    String? errorMessage,
  }) async {
    try {
      await operation();
    } catch (e) {
      if (errorMessage != null) {
        log("$errorMessage: $e");
      }
    }
  }
}

/// Extension methods for common safe operations
extension SafeStateNotifierExtension<T> on StateNotifier<T> {
  
  /// Check if the notifier is mounted and log if not
  bool get isSafeToUpdate {
    if (!mounted) {
      log("Warning: Attempted to update disposed StateNotifier of type ${runtimeType}");
      return false;
    }
    return true;
  }

  /// Safely set state with logging
  void setSafeState(T newState) {
    if (isSafeToUpdate) {
      state = newState;
    }
  }
}
