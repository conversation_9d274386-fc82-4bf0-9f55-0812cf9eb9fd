import 'dart:async';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:healo/common/utils/clear_all_controllers.dart';
import 'package:healo/common/utils/size.dart';
import 'package:healo/common/utils/snackbar.dart';
import 'package:healo/common/widgets/custom_button.dart';
import 'package:healo/common/widgets/custom_circularprogressindicator.dart';
import 'package:healo/common/widgets/custom_textformfield.dart';
import 'package:healo/constants/app_colors.dart';
import 'package:healo/providers/nav_provider.dart';
import 'package:healo/route/route_constants.dart';
import 'package:pinput/pinput.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:healo/providers/verify_otp_provider.dart';

class VefifyOTP extends ConsumerStatefulWidget {
  const VefifyOTP({super.key});

  @override
  ConsumerState<VefifyOTP> createState() => _VefifyOTPState();
}

class _VefifyOTPState extends ConsumerState<VefifyOTP> {
  final _phoneNumberController = TextEditingController();
  final _otpController = TextEditingController();
  bool _isVerifying = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final arguments =
          ModalRoute.of(context)!.settings.arguments as Map<String, dynamic>?;
      if (arguments != null) {
        ref.read(verifyOtpProvider.notifier).initialize(
              verificationId: arguments['verificationId'],
              phoneNumber: arguments['phoneNumber'],
            );
      }
    });
  }

  @override
  void dispose() {
    _otpController.dispose();
    super.dispose();
  }

  void clear() {
    clearControllers([_phoneNumberController, _otpController]);
  }

  Future<void> _verifyOTP() async {
    if (_otpController.text.length != 6) {
      customSnackBar(context, "Please enter a valid 6-digit OTP");
      return;
    }

    setState(() {
      _isVerifying = true;
    });

    try {
      final isCompleted = await ref
          .read(verifyOtpProvider.notifier)
          .verifyOTP(_otpController.text.trim());

      if (mounted) {
        if (isCompleted) {
          ref.read(bottomNavIndexProvider.notifier).state = 0;
          Navigator.pushNamedAndRemoveUntil(
              context, mainScreen, (route) => false);
        } else {
          Navigator.pushNamedAndRemoveUntil(
              context, initialDataScreen, (route) => false);
        }
      }
    } catch (e) {
      log("Verification failed: $e");
      if (mounted) {
        String errorMessage = e.toString();
        if (errorMessage.contains("Exception: ")) {
          errorMessage = errorMessage.replaceAll("Exception: ", "");
        }
        customSnackBar(context, errorMessage, color: AppColors.red);
      }
    } finally {
      if (mounted) {
        setState(() {
          _isVerifying = false;
        });
      }
    }
  }

  void _resendOTP() {
    ref.read(verifyOtpProvider.notifier).resendOTP();
  }

  @override
  Widget build(BuildContext context) {
    final otpState = ref.watch(verifyOtpProvider);

    return SafeArea(
      child: Scaffold(
        resizeToAvoidBottomInset: false,
        backgroundColor: Theme.of(context).scaffoldBackgroundColor,
        body: Stack(children: [
          Positioned.fill(
            child: SvgPicture.asset(
              "assets/svg/background_image.svg",
              fit: BoxFit.fill,
            ),
          ),
          Container(
            height: MediaQuery.of(context).size.height,
            padding: EdgeInsets.symmetric(horizontal: MySize.size38),
            child: Column(
              spacing: MySize.size20,
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Space.height(10),
                Center(
                  child: Image.asset(
                    "assets/png/helthy_logo.png",
                    height: MySize.size80,
                    width: MySize.size80,
                  ),
                ),
                Space.height(40),
                CustomTextformfield(
                    label: 'Your Phone Number',
                    controller:
                        TextEditingController(text: otpState.phoneNumber),
                    keyboardType: TextInputType.phone,
                    readOnly: true,
                    fillColor: AppColors.primaryColorWithAlpha30,
                    prefixIcon: Icon(Icons.phone),
                    suffixIcon: InkWell(
                      onTap: () {
                        Navigator.pop(context);
                      },
                      child: const Icon(
                        Icons.edit,
                        color: Colors.blue,
                      ),
                    )
                    // Render,
                    ),
                Text(
                  "Enter OTP",
                  style: TextStyle(
                      fontSize: MySize.size15, fontWeight: FontWeight.w400),
                ),
                Column(
                  spacing: MySize.size20,
                  children: [
                    Pinput(
                      controller: _otpController,
                      keyboardType: TextInputType.number,
                      length: 6,
                      defaultPinTheme: PinTheme(
                        width: MySize.size50,
                        height: MySize.size53,
                        textStyle: TextStyle(fontSize: MySize.size20),
                        decoration: BoxDecoration(
                            color: Theme.of(context).cardColor,
                            borderRadius: Shape.circular(MySize.size12),
                            border: Border.all(color: Colors.grey)),
                      ),
                    ),
                    _resendOTPWidget(otpState),
                    SizedBox(
                      width: SizeConfig.screenWidth! * .5,
                      child: _isVerifying
                          ? Center(child: CustomCircularProgressIndicator())
                          : CustomButton(
                              onTap: _verifyOTP,
                              text: 'Verify',
                            ),
                    ),
                  ],
                )
              ],
            ),
          ),
        ]),
      ),
    );
  }

  Widget _resendOTPWidget(VerifyOtpState otpState) {
    final minutes = (otpState.secondsLeft ~/ 60).toString().padLeft(2, '0');
    final seconds = (otpState.secondsLeft % 60).toString().padLeft(2, '0');

    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text("Didn't get OTP? ", style: TextStyle(fontSize: MySize.size12)),
        const Spacer(),
        Row(
          children: [
            InkWell(
              onTap: otpState.canResendOTP ? _resendOTP : null,
              child: Text(
                "Resend OTP ",
                style: TextStyle(
                  fontSize: MySize.size12,
                  color: otpState.canResendOTP
                      ? AppColors.primaryColor
                      : Theme.of(context).textTheme.bodyMedium!.color,
                ),
              ),
            ),
            Text(
              "in $minutes:$seconds",
              style: TextStyle(
                  fontSize: MySize.size12, fontWeight: FontWeight.bold),
            )
          ],
        )
      ],
    );
  }
}