import 'package:dotted_border/dotted_border.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:healo/common/widgets/blood_pressure_cards.dart';
import 'package:healo/common/widgets/goal_cards.dart';
import 'package:healo/common/widgets/kidney_card.dart';
import 'package:healo/common/widgets/liver_cards.dart';
import 'package:healo/common/widgets/menstrual_cycle_cards.dart';
import 'package:healo/common/widgets/sleep_boxes.dart';
import 'package:healo/common/widgets/thyroid_cards.dart';
import 'package:healo/constants/app_colors.dart';
import 'package:healo/models/daily_medication_model.dart';
import 'package:healo/models/monthly_medication_model.dart';
import 'package:healo/models/weekly_medication_model.dart';
import 'package:healo/providers/blood_pressure_provider.dart';
import 'package:healo/providers/bmi_provider.dart';
import 'package:healo/providers/hba1c_provider.dart';
import 'package:healo/providers/health_provider.dart';
import 'package:healo/route/route_constants.dart';
import 'package:healo/screens/report_screen.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:healo/providers/user_provider.dart';
import 'package:healo/providers/water_intake_provider.dart';
import 'package:percent_indicator/percent_indicator.dart';
import 'package:healo/providers/diabetes_provider.dart';
import 'package:healo/common/utils/size.dart';
import 'package:healo/providers/kidney_provider.dart';
import 'package:healo/providers/thyroid_provider.dart';
import 'package:healo/providers/liver_provider.dart';
import 'package:healo/providers/period_provider.dart';
import 'package:healo/providers/medication_provider.dart';
import 'package:healo/providers/report_provider.dart';
import 'package:healo/providers/health_data_loader_provider.dart';

class HomeScreen extends ConsumerStatefulWidget {
  const HomeScreen({super.key});

  @override
  ConsumerState<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends ConsumerState<HomeScreen> {
  @override
  void initState() {
    super.initState();
    // Load all health data at once using the health data loader provider
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(healthDataLoaderProvider.notifier).loadAllHealthData();
    });
  }

  @override
  Widget build(BuildContext context) {

    final userName = ref.watch(userNameProvider);
    final userGender = ref.watch(userGenderProvider);
    final updatedToday = ref.watch(updatedTodayProvider);
    int glasses = updatedToday == false ? 0 : ref.watch(glassesTodayProvider);
    final glassesPerDay = ref.watch(glassesPerDayProvider);
    final double bmi = ref.watch(bmiProvider);
    final double height = ref.watch(heightProvider);
    final double weight = ref.watch(weightProvider);
    final latestSugarReading = ref.watch(latestSugarReadingProvider);
    final periodData = ref.watch(periodProvider);
    final dailyMedicationData = ref.watch(medicationProvider);
    final weeklyMedicationData = ref.watch(weeklyMedicationProvider);
    final monthlyMedicationData = ref.watch(monthlyMedicationProvider);
    final List<DailyMedication> dailyMedicationList =
        dailyMedicationData.toList();
    final List<WeeklyMedication> weeklyMedicationList =
        weeklyMedicationData.toList();
    final List<MonthlyMedication> monthlyMedicationList =
        monthlyMedicationData.toList();
    final reportState = ref.watch(reportListProvider);
    final estimatedHba1c = ref.watch(estimatedHba1cProvider);

    return SafeArea(
      child: Scaffold(
          backgroundColor: Theme.of(context).scaffoldBackgroundColor,
          body: RefreshIndicator(
            onRefresh: () async {
              await ref
                  .read(healthDataLoaderProvider.notifier)
                  .refreshAllHealthData();
            },
            child: SingleChildScrollView(
              physics: AlwaysScrollableScrollPhysics(),
              child: Padding(
                padding: EdgeInsets.all(MySize.size15),
                child: Column(
                    spacing: MySize.size20,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                _getGreeting(),
                                style: TextStyle(
                                    fontSize: MySize.size14,
                                    color: Theme.of(context)
                                        .textTheme
                                        .bodySmall
                                        ?.color),
                              ),
                              Text(
                                "Hi, ${userName != null
                                    ? (userName.contains(' ')
                                        ? userName.substring(0, userName.indexOf(' '))
                                        : userName)
                                    : "User"}",
                                style: TextStyle(
                                  color: Theme.of(context)
                                      .textTheme
                                      .bodyLarge
                                      ?.color,
                                  fontSize: MySize.size20,
                                  fontWeight: FontWeight.w800,
                                ),
                              ),
                            ],
                          ),
                          Container(
                            width: MySize.size40,
                            height: MySize.size40,
                            decoration: BoxDecoration(
                              boxShadow: [
                                BoxShadow(
                                    color: Colors.black.withAlpha(90),
                                    spreadRadius: 1,
                                    blurRadius: 3,
                                    offset: const Offset(0, 2))
                              ],
                              borderRadius: Shape.circular(MySize.size14),
                              color: AppColors.white,
                            ),
                            child: InkWell(
                              onTap: () {},
                              child: Padding(
                                padding: EdgeInsets.all(MySize.size8),
                                child: SvgPicture.asset(
                                  "assets/svg/notification_icon.svg",
                                  height: MySize.size24,
                                  width: MySize.size24,
                                ),
                              ),
                            ),
                          )
                        ],
                      ),
                      Row(spacing: MySize.size15, children: [
                        Expanded(
                            child: GoalCards(
                          text: "Medication",
                          icon: SvgPicture.asset(
                            "assets/svg/medication_icon.svg",
                            height: MySize.size10,
                            width: MySize.size10,
                          ),
                          quantity: "Add or upload",
                          onTap: () {
                            if (dailyMedicationList.isEmpty &&
                                weeklyMedicationList.isEmpty &&
                                monthlyMedicationList.isEmpty) {
                              Navigator.pushNamed(
                                  context, setupMedicationScreen);
                            } else {
                              Navigator.pushNamed(
                                  context, medicationListScreen);
                            }
                          },
                        )),
                        Expanded(
                            child: GoalCards(
                          text: "Reports",
                          icon: SvgPicture.asset(
                            "assets/svg/add_report_icon.svg",
                            height: MySize.size28,
                            width: MySize.size28,
                          ),
                          quantity: reportState.when(
                              data: (reports) => reports.isEmpty
                                  ? "0 reports"
                                  : "${reports.length} reports",
                              loading: () => "Loading...",
                              error: (e, _) => "Error"),
                          onTap: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => ReportScreen(),
                              ),
                            );
                          },
                        ))
                      ]),
                      Row(
                        spacing: MySize.size15,
                        children: [
                          Expanded(
                            child: GoalCards(
                              text: "Water intake",
                              icon: SvgPicture.asset(
                                "assets/svg/water_intake_icon.svg",
                                height: MySize.size19,
                                width: MySize.size19,
                              ),
                              quantity: "$glasses/$glassesPerDay Glasses",
                              onTap: () {
                                Navigator.pushNamed(context, waterInTakeScreen);
                              },
                            ),
                          ),
                          Expanded(
                              child: GoalCards(
                            text: "Book a test",
                            icon: SvgPicture.asset(
                              "assets/svg/book_a_test_icon.svg",
                              height: MySize.size24,
                              width: MySize.size24,
                            ),
                            quantity: "At home",
                            onTap: () {
                              Navigator.pushNamed(context, bookTestScreen);
                            },
                          ))
                        ],
                      ),
                      Container(
                          padding: EdgeInsets.all(MySize.size15),
                          decoration: BoxDecoration(
                              borderRadius: Shape.circular(MySize.size10),
                              color: Theme.of(context).cardColor),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.start,
                            spacing: MySize.size10,
                            children: [
                              Row(
                                spacing: MySize.size50,
                                children: [
                                  Text(
                                    "Your Overall Health",
                                    style: TextStyle(
                                        fontSize: MySize.size18,
                                        fontWeight: FontWeight.bold),
                                  ),
                                  Text(
                                    "Result",
                                    style: TextStyle(
                                        fontSize: MySize.size12,
                                        fontWeight: FontWeight.bold,
                                        color: AppColors.red),
                                  ),
                                ],
                              ),
                              Text(
                                "Your health needs improvement. Let’s focus on your Heart health, Kidney Health and Liver Health.\n\n"
                                "Click on each tile to know more about your health.\n",
                                style: TextStyle(
                                    fontSize: 12,
                                    fontWeight: FontWeight.normal),
                              )
                            ],
                          )),
                      activityWidget(),
                      bmiWidget(bmi, height, weight),
                      sleepWidget(),
                      bloodPressureWidget(),
                      if (userGender == "Female")
                        menstruationWidget(userGender, periodData),
                      diabetesWidget(latestSugarReading, estimatedHba1c),
                      kidneyWidget(),
                      thyroidWidget(),
                      liverWidget(),
                      vitaminsWidget(),
                    ]
                  ),
              ),
            ),
          )),
    );
  }

  Widget activityWidget() {
    final healthData = ref.watch(healthDataProvider);
    final healthNotifier = ref.watch(healthDataProvider.notifier);

    // Calculate progress percentages
    final stepProgress = healthNotifier.getStepProgressPercentage();
    final distanceProgress = healthNotifier.getDistanceProgressPercentage();
    final calorieProgress = healthNotifier.getCalorieProgressPercentage();

    // Get goals
    final stepGoal = healthNotifier.getDailyStepGoal();
    final distanceGoal = healthNotifier.getDailyDistanceGoal();
    final calorieGoal = healthNotifier.getDailyCalorieGoal();

    // Check if we have any health data
    final bool hasNoHealthData = (healthData.steps ?? 0) <= 0 &&
        (healthData.distance ?? 0) <= 0 &&
        (healthData.calories ?? 0) <= 0;

    return InkWell(
      onTap: () {
        Navigator.pushNamed(context, healthDetailScreen);
      },
      child: Container(
        padding: EdgeInsets.all(MySize.size15),
        decoration: BoxDecoration(
            borderRadius: Shape.circular(MySize.size10),
            color: Theme.of(context).cardColor),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          spacing: MySize.size10,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  "Activity",
                  style: TextStyle(
                      fontSize: MySize.size18, fontWeight: FontWeight.bold),
                ),
                Row(
                  children: [
                    // Add refresh button if no health data
                    if (hasNoHealthData)
                      IconButton(
                        icon: Icon(
                          Icons.refresh,
                          size: MySize.size18,
                          color: AppColors.primaryColor,
                        ),
                        onPressed: () async {
                          // Show loading indicator
                          ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                            content: Text('Refreshing all health data...'),
                            duration: Duration(seconds: 1),
                          ));

                          // Refresh all health data including sleep data
                          await healthNotifier.refreshHealthData();

                          // Show success message
                          Future.delayed(Duration(milliseconds: 500), () {
                            if (mounted && context.mounted) {
                              ScaffoldMessenger.of(context)
                                  .showSnackBar(SnackBar(
                                content: Text('All health data refreshed'),
                                duration: Duration(seconds: 1),
                              ));
                            }
                          });
                        },
                      ),
                    Icon(
                      Icons.arrow_forward_ios,
                      size: MySize.size16,
                      color: AppColors.textGray,
                    ),
                  ],
                ),
              ],
            ),
            Space.height(10),
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Expanded(
                  child: Column(
                    children: [
                      CircularPercentIndicator(
                        radius: MySize.size50,
                        lineWidth: MySize.size10,
                        percent: stepProgress,
                        center: SvgPicture.asset(
                          "assets/svg/steps_icon.svg",
                          height: MySize.size30,
                          width: MySize.size30,
                        ),
                        backgroundColor:
                            AppColors.progressBackground.withAlpha(31),
                        progressColor: AppColors.primaryColor,
                      ),
                      Space.height(10),
                      Text(
                        "Steps",
                        style: TextStyle(
                            fontSize: MySize.size14,
                            fontWeight: FontWeight.w600),
                      ),
                      Space.height(10),
                      Text(
                        "${healthData.steps ?? 0}/$stepGoal Steps",
                        style: TextStyle(
                            fontSize: MySize.size12,
                            color: AppColors.textGray,
                            fontWeight: FontWeight.w500),
                      )
                    ],
                  ),
                ),
                Expanded(
                  child: Column(
                    children: [
                      CircularPercentIndicator(
                        radius: MySize.size50,
                        lineWidth: MySize.size10,
                        percent: distanceProgress,
                        center: SvgPicture.asset(
                          "assets/svg/distance_icon.svg",
                          height: MySize.size30,
                          width: MySize.size30,
                        ),
                        backgroundColor:
                            AppColors.progressBackground.withAlpha(31),
                        progressColor: AppColors.primaryColor,
                      ),
                      Space.height(10),
                      Text(
                        "Distance",
                        style: TextStyle(
                            fontSize: MySize.size14,
                            fontWeight: FontWeight.w600),
                      ),
                      Space.height(10),
                      Text(
                        "${healthNotifier.getFormattedDistance()}/$distanceGoal Kms",
                        style: TextStyle(
                            fontSize: MySize.size12,
                            color: AppColors.textGray,
                            fontWeight: FontWeight.w500),
                      )
                    ],
                  ),
                ),
                Expanded(
                  child: Column(
                    children: [
                      CircularPercentIndicator(
                        radius: MySize.size50,
                        lineWidth: MySize.size10,
                        percent: calorieProgress,
                        center: SvgPicture.asset(
                          "assets/svg/calories_icon.svg",
                          height: MySize.size30,
                          width: MySize.size30,
                        ),
                        backgroundColor:
                            AppColors.progressBackground.withAlpha(31),
                        progressColor: AppColors.primaryColor,
                      ),
                      Space.height(10),
                      Text(
                        "Calories",
                        style: TextStyle(
                            fontSize: MySize.size14,
                            fontWeight: FontWeight.w600),
                      ),
                      Space.height(10),
                      Text(
                        "${healthData.calories?.toInt() ?? 0}/${calorieGoal.toInt()} Cal",
                        style: TextStyle(
                            fontSize: MySize.size12,
                            color: AppColors.textGray,
                            fontWeight: FontWeight.w500),
                      )
                    ],
                  ),
                )
              ],
            )
          ],
        ),
      ),
    );
  }

  Widget bmiWidget(double bmi, double height, double weight) {
    return InkWell(
      onTap: () {
        Navigator.pushNamed(context, bmiScreen);
      },
      child: Row(
        children: [
          Container(
            width: SizeConfig.screenWidth! * 0.46,
            padding: EdgeInsets.all(MySize.size10),
            decoration: BoxDecoration(
                borderRadius: Shape.circular(MySize.size10),
                color: Theme.of(context).cardColor),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              spacing: MySize.size10,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      "BMI",
                      style: TextStyle(
                          fontSize: MySize.size18, fontWeight: FontWeight.bold),
                    ),
                  ],
                ),
                CircularPercentIndicator(
                  radius: MySize.size70,
                  lineWidth: MySize.size12,
                  percent: 0.5,
                  center: Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          bmi.toStringAsFixed(1),
                          style: TextStyle(
                              fontSize: MySize.size23,
                              fontWeight: FontWeight.w600),
                        ),
                        Space.height(5),
                        Text(
                          "Your BMI",
                          style: TextStyle(
                              fontSize: MySize.size12,
                              color: AppColors.textGray,
                              fontWeight: FontWeight.w500),
                        ),
                      ],
                    ),
                  ),
                  backgroundColor: AppColors.progressBackground.withAlpha(31),
                  progressColor: AppColors.primaryColor,
                ),
                Space.height(10)
              ],
            ),
          ),
          Space.width(10),
          Wrap(
            direction: Axis.vertical,
            spacing: MySize.size20,
            children: [
              Container(
                width: SizeConfig.screenWidth! * 0.4,
                height: MySize.size70,
                padding: EdgeInsets.symmetric(horizontal: MySize.size10),
                decoration: BoxDecoration(
                  color: Theme.of(context).cardColor,
                  borderRadius: Shape.circular(MySize.size10),
                  border: Border.all(
                    color: AppColors.primaryColor,
                    width: MySize.size1,
                  ),
                ),
                child: Row(
                  children: [
                    Container(
                      height: MySize.size40,
                      width: MySize.size40,
                      padding: EdgeInsets.all(MySize.size9),
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: AppColors.primaryColor,
                      ),
                      child: SvgPicture.asset("assets/svg/height_icon.svg"),
                    ),
                    Space.width(10),
                    Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Height',
                          style: TextStyle(
                              fontSize: MySize.size12,
                              fontWeight: FontWeight.w400,
                              color: Colors.grey),
                        ),
                        Row(
                          children: [
                            Text(
                              height.toStringAsFixed(1),
                              style: TextStyle(
                                fontSize: MySize.size18,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            Space.width(5),
                            Text(
                              'cm',
                              style: TextStyle(
                                  fontSize: MySize.size11,
                                  fontWeight: FontWeight.w400,
                                  color: Colors.grey),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              Container(
                height: MySize.size70,
                width: SizeConfig.screenWidth! * 0.4,
                padding: EdgeInsets.symmetric(horizontal: MySize.size10),
                decoration: BoxDecoration(
                  color: Theme.of(context).cardColor,
                  border: Border.all(
                    color: AppColors.primaryColor,
                    width: MySize.size1,
                  ),
                  borderRadius: Shape.circular(MySize.size10),
                ),
                child: Row(
                  children: [
                    Container(
                      height: MySize.size40,
                      width: MySize.size40,
                      padding: EdgeInsets.all(MySize.size9),
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: AppColors.primaryColor,
                      ),
                      child: SvgPicture.asset("assets/svg/weight_icon.svg"),
                    ),
                    Space.width(10),
                    Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Weight',
                          style: TextStyle(
                              fontSize: MySize.size12,
                              fontWeight: FontWeight.w400,
                              color: Colors.grey),
                        ),
                        Row(
                          children: [
                            Text(
                              weight.toStringAsFixed(1),
                              style: TextStyle(
                                fontSize: MySize.size18,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            Space.width(5),
                            Text(
                              'Kgs',
                              style: TextStyle(
                                  fontSize: MySize.size11,
                                  fontWeight: FontWeight.w400,
                                  color: Colors.grey),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          )
        ],
      ),
    );
  }

  Widget sleepWidget() {
    final healthData = ref.watch(healthDataProvider);

    // Calculate sleep quality percentage (assuming 8 hours is 100%)
    final sleepHours = healthData.sleepHours ?? 0;
    final sleepQualityPercent = (sleepHours / 8.0).clamp(0.0, 1.0);
    final sleepQualityDisplay = (sleepQualityPercent * 100).toInt();

    // Format sleep hours and minutes
    final sleepHoursInt = sleepHours.floor();
    final sleepMinutes = ((sleepHours - sleepHoursInt) * 60).round();
    final totalSleepFormatted = "${sleepHoursInt}h ${sleepMinutes}m";

    // For demo purposes, we'll set some reasonable values for deep and light sleep
    final deepSleepHours = (sleepHours * 0.3).clamp(0.0, 8.0);
    final deepSleepHoursInt = deepSleepHours.floor();
    final deepSleepMinutes =
        ((deepSleepHours - deepSleepHoursInt) * 60).round();
    final deepSleepFormatted = "${deepSleepHoursInt}h ${deepSleepMinutes}m";

    final lightSleepHours = (sleepHours * 0.7).clamp(0.0, 8.0);
    final lightSleepHoursInt = lightSleepHours.floor();
    final lightSleepMinutes =
        ((lightSleepHours - lightSleepHoursInt) * 60).round();
    final lightSleepFormatted = "${lightSleepHoursInt}h ${lightSleepMinutes}m";

    return InkWell(
      onTap: () => Navigator.pushNamed(context, sleepAnalysisScreen),
      child: Container(
        padding: EdgeInsets.all(MySize.size15),
        decoration: BoxDecoration(
            borderRadius: Shape.circular(MySize.size10),
            color: Theme.of(context).cardColor),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          spacing: MySize.size10,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  "Sleep Analysis",
                  style: TextStyle(
                      fontSize: MySize.size18, fontWeight: FontWeight.bold),
                ),
                Row(
                  children: [
                    Text(
                      "Today",
                      style: TextStyle(
                          fontSize: MySize.size12,
                          fontWeight: FontWeight.w500,
                          color: Theme.of(context).textTheme.bodySmall!.color),
                    ),
                  ],
                ),
              ],
            ),
            Space.height(10),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CircularPercentIndicator(
                  radius: MySize.size70,
                  lineWidth: MySize.size12,
                  percent: sleepQualityPercent,
                  center: Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text("Sleep Quality",
                            style: TextStyle(
                                fontSize: MySize.size12,
                                fontWeight: FontWeight.w500)),
                        Space.height(5),
                        Text("$sleepQualityDisplay%",
                            style: TextStyle(
                                fontSize: MySize.size23,
                                fontWeight: FontWeight.w600)),
                      ],
                    ),
                  ),
                  backgroundColor: AppColors.progressBackground.withAlpha(31),
                  progressColor: AppColors.primaryColor,
                ),
                Space.width(20),
                Column(
                  children: [
                    Row(
                      children: [
                        SleepBoxes(title: "Deep", value: deepSleepFormatted),
                        Space.width(10),
                        SleepBoxes(title: "Light", value: lightSleepFormatted),
                      ],
                    ),
                    Space.height(10),
                    Row(
                      children: [
                        SleepBoxes(title: "Nap", value: "0m"),
                        Space.width(10),
                        SleepBoxes(
                            title: "Total Sleep", value: totalSleepFormatted),
                      ],
                    ),
                  ],
                ),
              ],
            ),
            Space.height(10)
          ],
        ),
      ),
    );
  }

  Widget bloodPressureWidget() {
    // Get the latest blood pressure reading from the provider
    final latestReading = ref.watch(latestBloodPressureReadingProvider);

    // Get heart rate from health data provider
    final heartRate = ref.watch(healthDataProvider).heartRate?.toInt() ?? 00;

    // Extract values from the latest reading or use defaults if no reading exists
    final systolic = latestReading?['systolic']?.toString() ?? "00";
    final diastolic = latestReading?['diastolic']?.toString() ?? "00";
    final pulseRate =
        latestReading?['pulse_rate']?.toString() ?? heartRate.toString();

    return InkWell(
      onTap: () => Navigator.pushNamed(context, bloodPressureScreen),
      child: Container(
        padding: EdgeInsets.all(MySize.size15),
        decoration: BoxDecoration(
          borderRadius: Shape.circular(MySize.size10),
          color: Theme.of(context).cardColor,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              "Blood Pressure",
              style: TextStyle(
                fontSize: MySize.size18,
                fontWeight: FontWeight.bold,
              ),
            ),
            Space.height(10),
            Wrap(
              spacing: MySize.size10,
              runSpacing: MySize.size10,
              children: [
                BPCards(
                  title: "Systolic",
                  value: systolic,
                  icon: "assets/svg/systolic_icon.svg",
                  unit: "mmHg",
                ),
                BPCards(
                  title: "Diastolic",
                  value: diastolic,
                  icon: "assets/svg/heart_icon.svg",
                  unit: "mmHg",
                ),
                BPCards(
                  title: "Heart Rate",
                  value: pulseRate,
                  icon: "assets/svg/heart_rate_icon.svg",
                  unit: "bpm",
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget diabetesWidget(latestSugarReading, estimatedHba1c) {
    // Get the latest HbA1c reading
    final latestValue = ref.watch(latestHba1cReadingProvider);
    final latestDate = ref.watch(latestHba1cDateProvider);

    // Format HbA1c value with one decimal place
    final hba1cDisplay =
        latestValue != null ? "${latestValue.toStringAsFixed(1)}%" : "N/A";

    // Format the date text
    String dateText = "Not Measured Yet";
    if (latestDate != null) {
      try {
        final parts = latestDate.split('-');
        if (parts.length == 3) {
          final day = int.parse(parts[0]);
          final month = int.parse(parts[1]);
          final year = int.parse(parts[2]);
          final date = DateTime(year, month, day);
          final now = DateTime.now();
          final difference = now.difference(date);

          if (difference.inDays < 1) {
            dateText = "Recent HbA1c\nToday";
          } else if (difference.inDays == 1) {
            dateText = "Recent HbA1c\nYesterday";
          } else if (difference.inDays < 30) {
            dateText = "Recent HbA1c\n${difference.inDays} days ago";
          } else if (difference.inDays < 60) {
            dateText = "Recent HbA1c\n1 month ago";
          } else {
            final months = (difference.inDays / 30).floor();
            dateText = "Recent HbA1c\n$months months ago";
          }
        }
      } catch (e) {
        dateText = "Not Measured Yet";
      }
    }

    return Container(
      padding: EdgeInsets.all(MySize.size15),
      decoration: BoxDecoration(
          borderRadius: Shape.circular(MySize.size10),
          color: Theme.of(context).cardColor),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        spacing: MySize.size10,
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                padding: EdgeInsets.all(MySize.size15),
                decoration: BoxDecoration(
                  borderRadius: Shape.circular(MySize.size10),
                  color: Theme.of(context).cardColor,
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      "Diabetes Management",
                      style: TextStyle(
                        fontSize: MySize.size18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
              Space.height(10),
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(
                    flex: 1,
                    child: InkWell(
                      onTap: () {
                        Navigator.pushNamed(context, '/hba1cScreen');
                      },
                      child: Container(
                        height: MySize.size180,
                        padding: EdgeInsets.all(MySize.size10),
                        decoration: BoxDecoration(
                          color: AppColors.primaryColor,
                          borderRadius: Shape.circular(MySize.size10),
                        ),
                        child: Stack(
                          children: [
                            Positioned(
                              top: 0,
                              left: 0,
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Container(
                                    width: MySize.size40,
                                    height: MySize.size40,
                                    decoration: BoxDecoration(
                                      color: AppColors.white,
                                      shape: BoxShape.circle,
                                    ),
                                    child: Center(
                                      child: SvgPicture.asset(
                                        'assets/svg/diabetes_icon.svg',
                                        width: MySize.size24,
                                        height: MySize.size24,
                                      ),
                                    ),
                                  ),
                                  Space.height(10),
                                  Text(
                                    dateText,
                                    style: TextStyle(color: AppColors.white),
                                  ),
                                ],
                              ),
                            ),
                            Positioned(
                              top: 0,
                              right: 0,
                              child: Text(
                                hba1cDisplay,
                                style: TextStyle(
                                  color: AppColors.white,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                            Positioned(
                              bottom: 0,
                              right: 0,
                              child: Text(
                                latestValue != null
                                    ? latestValue < 5.7
                                        ? "Normal"
                                        : latestValue < 6.5
                                            ? "Pre-Diabetes"
                                            : "Diabetes"
                                    : "N/A",
                                style: TextStyle(
                                  color: AppColors.white,
                                  fontSize: MySize.size12,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                  Space.width(10),
                  Expanded(
                    flex: 1,
                    child: Column(
                      children: [
                        Container(
                          height: MySize.size90,
                          width: double.infinity,
                          padding: EdgeInsets.all(MySize.size8),
                          decoration: BoxDecoration(
                            color: Theme.of(context).scaffoldBackgroundColor,
                            borderRadius: Shape.circular(MySize.size10),
                          ),
                          child: Stack(
                            children: [
                              Positioned(
                                top: 0,
                                left: 0,
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Container(
                                      width: MySize.size35,
                                      height: MySize.size35,
                                      decoration: BoxDecoration(
                                        color: AppColors.primaryColor,
                                        shape: BoxShape.circle,
                                      ),
                                      child: Center(
                                        child: SvgPicture.asset(
                                          'assets/svg/diabetes_icon.svg',
                                          width: MySize.size22,
                                          height: MySize.size22,
                                          colorFilter: ColorFilter.mode(
                                              AppColors.white, BlendMode.srcIn),
                                        ),
                                      ),
                                    ),
                                    Space.height(15),
                                    Text(
                                      'Estimated HbA1c',
                                      style: TextStyle(
                                        fontSize: MySize.size12,
                                        color: AppColors.textGray,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              Positioned(
                                top: 0,
                                right: 0,
                                child: Text(
                                  "$estimatedHba1c%",
                                  style: TextStyle(
                                    fontWeight: FontWeight.bold,
                                    fontSize: MySize.size20,
                                    color: Theme.of(context)
                                        .textTheme
                                        .bodyMedium!
                                        .color,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                        Space.height(10),
                        InkWell(
                          onTap: () {
                            Navigator.pushNamed(context, '/diabetesScreen');
                          },
                          child: Container(
                            height: MySize.size90,
                            width: double.infinity,
                            padding: EdgeInsets.all(MySize.size8),
                            decoration: BoxDecoration(
                              color: Theme.of(context).scaffoldBackgroundColor,
                              borderRadius: BorderRadius.circular(10),
                            ),
                            child: Stack(
                              children: [
                                Positioned(
                                  top: 0,
                                  left: 0,
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Container(
                                        width: MySize.size35,
                                        height: MySize.size35,
                                        decoration: BoxDecoration(
                                          color: AppColors.primaryColor,
                                          shape: BoxShape.circle,
                                        ),
                                        child: Center(
                                          child: SvgPicture.asset(
                                            'assets/svg/blood_sugar_icon.svg',
                                            width: MySize.size22,
                                            height: MySize.size22,
                                            colorFilter: ColorFilter.mode(
                                                AppColors.white,
                                                BlendMode.srcIn),
                                          ),
                                        ),
                                      ),
                                      Space.height(15),
                                      Text(
                                        'Blood Sugar',
                                        style: TextStyle(
                                          fontSize: MySize.size12,
                                          color: AppColors.textGray,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                Positioned(
                                  top: 0,
                                  right: 0,
                                  child: Column(
                                    children: [
                                      Text(
                                        latestSugarReading.toString(),
                                        style: TextStyle(
                                          fontWeight: FontWeight.bold,
                                          fontSize: MySize.size20,
                                          color: Theme.of(context)
                                              .textTheme
                                              .bodyMedium!
                                              .color,
                                        ),
                                      ),
                                      Text(
                                        'mg/dL',
                                        style: TextStyle(
                                          fontSize: MySize.size12,
                                          color: AppColors.primaryColor,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              )
            ],
          )
        ],
      ),
    );
  }

  Widget kidneyWidget() {
    // Get the latest kidney readings from the provider
    final latestReadings = ref.watch(latestKidneyReadingsProvider);

    // Extract values or use defaults if no readings exist
    final gfr = latestReadings?['gfr']?.toString() ?? "N/A";
    final creatinine = latestReadings?['creatinine']?.toString() ?? "N/A";
    final bun = latestReadings?['bun']?.toString() ?? "N/A";
    final albumin = latestReadings?['albumin']?.toString() ?? "N/A";

    return InkWell(
      onTap: () => Navigator.pushNamed(context, kidneyManagementScreen),
      child: Container(
        padding: EdgeInsets.all(MySize.size15),
        decoration: BoxDecoration(
            borderRadius: Shape.circular(MySize.size10),
            color: Theme.of(context).cardColor),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          spacing: MySize.size10,
          children: [
            Text(
              "Kidney Management",
              style: TextStyle(
                  fontSize: MySize.size18, fontWeight: FontWeight.bold),
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              spacing: MySize.size5,
              children: [
                KidneyCard(
                  title: "GFR Rate",
                  value: gfr,
                  icon: "assets/svg/gfr_icon.svg",
                  unit: "U/L",
                ),
                KidneyCard(
                  title: "Creatinine",
                  value: creatinine,
                  icon: "assets/svg/creatinine_icon.svg",
                  unit: "mg/dl",
                ),
              ],
            ),
            Row(
              spacing: MySize.size5,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                KidneyCard(
                  title: "BUN",
                  value: bun,
                  icon: "assets/svg/bun_icon.svg",
                  unit: "mg/dl",
                ),
                KidneyCard(
                  title: "Albumin",
                  value: albumin,
                  icon: "assets/svg/albumin_icon.svg",
                  unit: "U/L",
                ),
              ],
            )
          ],
        ),
      ),
    );
  }

  Widget thyroidWidget() {
    // Get the latest thyroid reading from the provider
    final latestReading = ref.watch(latestThyroidReadingProvider);
    final thyroidStatus = ref.watch(thyroidStatusProvider);

    // Extract values or use defaults if no readings exist
    final tsh = latestReading?['tsh']?.toString() ?? "N/A";
    final t3 = latestReading?['t3']?.toString() ?? "N/A";
    final t4 = latestReading?['t4']?.toString() ?? "N/A";

    return InkWell(
      onTap: () => Navigator.pushNamed(context, thyroidManagementScreen),
      child: Container(
        padding: EdgeInsets.all(MySize.size15),
        decoration: BoxDecoration(
            borderRadius: Shape.circular(MySize.size10),
            color: Theme.of(context).cardColor),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          spacing: MySize.size10,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              spacing: MySize.size50,
              children: [
                Text(
                  "Thyroid Management",
                  style: TextStyle(
                      fontSize: MySize.size18, fontWeight: FontWeight.bold),
                ),
                if (thyroidStatus != "Unknown")
                  Text(
                    thyroidStatus,
                    style: TextStyle(
                      fontSize: MySize.size12,
                      fontWeight: FontWeight.bold,
                      color: thyroidStatus == "Normal"
                          ? AppColors.primaryColor
                          : AppColors.red,
                    ),
                  ),
              ],
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                ThyroidCard(
                  title: "TSH",
                  value: tsh,
                  icon: "assets/svg/tsh_icon.svg",
                  unit: "mIU/L",
                ),
                ThyroidCard(
                  title: "Free T3",
                  value: t3,
                  icon: "assets/svg/freet3_icon.svg",
                  unit: "pg/mL",
                ),
                ThyroidCard(
                  title: "Free T4",
                  value: t4,
                  icon: "assets/svg/freet4_icon.svg",
                  unit: "ng/dL",
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget liverWidget() {
    // Get the latest liver readings from the provider
    final latestReading = ref.watch(latestLiverReadingProvider);
    final liverStatus = ref.watch(liverHealthStatusProvider);

    // Extract values or use defaults if no readings exist
    final ast = latestReading?['ast']?.toString() ?? "N/A";
    final alt = latestReading?['alt']?.toString() ?? "N/A";
    final bilirubin = latestReading?['bilirubin']?.toString() ?? "N/A";
    final alp = latestReading?['alp']?.toString() ?? "N/A";

    return InkWell(
      onTap: () {
        Navigator.pushNamed(context, liverScreen);
      },
      child: Container(
        padding: EdgeInsets.all(MySize.size15),
        decoration: BoxDecoration(
            borderRadius: Shape.circular(MySize.size10),
            color: Theme.of(context).cardColor),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          spacing: MySize.size10,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  "Liver Management",
                  style: TextStyle(
                      fontSize: MySize.size18, fontWeight: FontWeight.bold),
                ),
                if (liverStatus != "Unknown")
                  Text(
                    liverStatus,
                    style: TextStyle(
                      fontSize: MySize.size12,
                      fontWeight: FontWeight.bold,
                      color: liverStatus == "Normal"
                          ? AppColors.primaryColor
                          : AppColors.red,
                    ),
                  ),
              ],
            ),
            GridView.count(
              shrinkWrap: true,
              physics: NeverScrollableScrollPhysics(),
              mainAxisSpacing: MySize.size5,
              crossAxisSpacing: MySize.size5,
              crossAxisCount: 2,
              children: [
                LiverCards(
                    title: 'Aspartate Aminotransferase',
                    value: ast,
                    icon: 'assets/svg/ast_icon.svg',
                    unit: 'U/L'),
                LiverCards(
                    title: 'Alanine Aminotransferase',
                    value: alt,
                    icon: 'assets/svg/ame_icon.svg',
                    unit: 'U/L'),
                LiverCards(
                    title: 'Bilirubin Levels',
                    value: bilirubin,
                    icon: 'assets/svg/bilirubin_icon.svg',
                    unit: 'mg/dL'),
                LiverCards(
                    title: 'Alkaline Phosphatase',
                    value: alp,
                    icon: 'assets/svg/alkaline_icon.svg',
                    unit: 'U/L'),
              ],
            )
          ],
        ),
      ),
    );
  }

  Widget menstruationWidget(String? userGender,
      Map<String, Map<String, dynamic>> periodData) {
    if (periodData.isEmpty) {
      return InkWell(
        onTap: () {
          Navigator.pushNamed(context, onboardingScreen);
        },
        child: Container(
          width: double.infinity,
          padding: EdgeInsets.all(MySize.size15),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(MySize.size10),
            color: Theme.of(context).cardColor,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Text(
                    "Menstrual Cycle",
                    style: TextStyle(
                        fontSize: MySize.size18, fontWeight: FontWeight.bold),
                  ),
                ],
              ),
              Space.height(20),
              DottedBorder(
                borderType: BorderType.Circle,
                dashPattern: [6, 3],
                color: AppColors.primaryColor,
                strokeWidth: 2,
                child: Container(
                  width: 150, // diameter of circle
                  height: 150,
                  alignment: Alignment.center,
                  child: Text(
                    "No Data Yet",
                    style: TextStyle(
                      fontSize: MySize.size18,
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).textTheme.bodySmall!.color,
                    ),
                  ),
                ),
              ),
              SizedBox(height: MySize.size20),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primaryColor,
                    padding: EdgeInsets.symmetric(vertical: MySize.size12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(MySize.size8),
                    ),
                  ),
                  onPressed: () {
                    Navigator.pushNamed(context, onboardingScreen);
                  },
                  child: Text(
                    "Start Tracking",
                    style: TextStyle(
                        fontSize: MySize.size16, color: AppColors.white),
                  ),
                ),
              ),
            ],
          ),
        ),
      );
    }

    return InkWell(
      onTap: () {
        Navigator.pushNamed(context, periodTrackerScreen);
      },
        child: Container(
          padding: EdgeInsets.all(MySize.size15),
          decoration: BoxDecoration(
              borderRadius: Shape.circular(MySize.size10),
              color: Theme.of(context).cardColor),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            spacing: MySize.size10,
            children: [
              Row(
                spacing: MySize.size60,
                children: [
                  Text(
                    "Menstrual Cycle",
                    style: TextStyle(
                        fontSize: MySize.size18, fontWeight: FontWeight.bold),
                  ),
                ],
              ),
              MenstrualCycleCards(
                  title: 'Cycle Length: 28',
                  value: 'Normal',
                  icon: 'assets/svg/cycle_length_icon.svg',
                  unit: 'Days'),
              MenstrualCycleCards(
                  title: 'Period Length: 4',
                  value: 'Normal',
                  icon: 'assets/svg/period_length_icon.svg',
                  unit: 'Days'),
              MenstrualCycleCards(
                  title: 'Period Status: 3',
                  value: 'Normal',
                  icon: 'assets/svg/period_status_icon.svg',
                  unit: 'Days'),
            ],
          ),
        ),
      );
  }

  Widget vitaminsWidget() {
    return Container(
      padding: EdgeInsets.all(MySize.size15),
      decoration: BoxDecoration(
          borderRadius: Shape.circular(MySize.size10),
          color: Theme.of(context).cardColor),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        spacing: MySize.size10,
        children: [
          Row(
            spacing: MySize.size50,
            children: [
              Text(
                "Vitamins and Minerals",
                style: TextStyle(
                    fontSize: MySize.size18, fontWeight: FontWeight.bold),
              ),
            ],
          ),
          Container(
            width: double.infinity,
            decoration: BoxDecoration(
              color: Theme.of(context).scaffoldBackgroundColor,
              borderRadius: Shape.circular(MySize.size10),
            ),
            child: Padding(
              padding: EdgeInsets.only(top: MySize.size8, bottom: MySize.size8),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  Column(
                    children: [
                      CircularPercentIndicator(
                        radius: MySize.size30,
                        lineWidth: MySize.size6,
                        percent: 3 / 5,
                        center: Text("3/5"),
                        backgroundColor:
                            AppColors.progressBackground.withValues(alpha: 31),
                        progressColor: AppColors.primaryColor,
                      ),
                      Text(
                        "Vitamins Taken",
                        style: TextStyle(
                            fontWeight: FontWeight.w500,
                            fontSize: MySize.size12),
                      ),
                    ],
                  ),
                  Column(
                    children: [
                      Container(
                        padding: EdgeInsets.all(MySize.size8),
                        width: MySize.size70,
                        decoration: BoxDecoration(
                          shape: BoxShape.rectangle,
                          borderRadius: Shape.circular(MySize.size10),
                          color: AppColors.primaryColor,
                        ),
                        child: SvgPicture.asset(
                          'assets/svg/next_schedule_icon.svg',
                          height: MySize.size24,
                          width: MySize.size24,
                        ),
                      ),
                      Space.height(10),
                      Text(
                        "Next Scheduled",
                        style: TextStyle(fontSize: MySize.size12),
                      ),
                      Text(
                        "omega-3 at 8:00 Pm",
                        style: TextStyle(
                            fontSize: MySize.size9, color: Colors.grey),
                      ),
                    ],
                  ),
                  Column(
                    children: [
                      Container(
                        padding: EdgeInsets.all(MySize.size8),
                        width: MySize.size70,
                        decoration: BoxDecoration(
                          shape: BoxShape.rectangle,
                          borderRadius: Shape.circular(MySize.size10),
                          color: AppColors.primaryColor,
                        ),
                        child: SvgPicture.asset(
                          'assets/svg/missed_intake_icon.svg',
                          height: MySize.size24,
                          width: MySize.size24,
                        ),
                      ),
                      Space.height(10),
                      Text(
                        "Missed Intake",
                        style: TextStyle(fontSize: MySize.size12),
                      ),
                      Text(
                        "1 Vitmain Yesterday",
                        style: TextStyle(
                            fontSize: MySize.size9, color: Colors.grey),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          Space.height(10),
          Container(
            width: double.infinity,
            decoration: BoxDecoration(
              color: Theme.of(context).scaffoldBackgroundColor,
              borderRadius: Shape.circular(MySize.size10),
            ),
            child: Padding(
              padding: EdgeInsets.symmetric(vertical: MySize.size10),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  Column(
                    children: [
                      Container(
                        padding: EdgeInsets.all(MySize.size8),
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: AppColors.primaryColor,
                        ),
                        child: SvgPicture.asset(
                          'assets/svg/calcium_icon.svg',
                          height: MySize.size24,
                          width: MySize.size24,
                        ),
                      ),
                      Space.height(6),
                      Text("9.2",
                          style: TextStyle(
                              fontSize: MySize.size18,
                              fontWeight: FontWeight.bold)),
                      Text("mg/DL",
                          style: TextStyle(
                              fontSize: MySize.size10, color: Colors.grey)),
                      Text("Calcium", style: TextStyle(fontSize: 10)),
                    ],
                  ),
                  Column(
                    children: [
                      Container(
                        padding: EdgeInsets.all(MySize.size8),
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: AppColors.primaryColor,
                        ),
                        child: SvgPicture.asset(
                          'assets/svg/magnesium_icon.svg',
                          height: MySize.size24,
                          width: MySize.size24,
                        ),
                      ),
                      Space.height(6),
                      Text("1.6",
                          style: TextStyle(
                              fontSize: MySize.size18,
                              fontWeight: FontWeight.bold)),
                      Text("mg/DL",
                          style: TextStyle(
                              fontSize: MySize.size10, color: Colors.grey)),
                      Text("Magnesium", style: TextStyle(fontSize: 10)),
                    ],
                  ),
                  Column(
                    children: [
                      Container(
                        padding: EdgeInsets.all(MySize.size8),
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: AppColors.primaryColor,
                        ),
                        child: SvgPicture.asset(
                          'assets/svg/phosphorus_icon.svg',
                          height: MySize.size24,
                          width: MySize.size24,
                        ),
                      ),
                      Space.height(6),
                      Text("9.2",
                          style: TextStyle(
                              fontSize: MySize.size18,
                              fontWeight: FontWeight.bold)),
                      Text("mg/DL",
                          style: TextStyle(
                              fontSize: MySize.size10, color: Colors.grey)),
                      Text("Phosphorus",
                          style: TextStyle(fontSize: MySize.size10)),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _getGreeting() {
    final hour = DateTime.now().hour;
    if (hour < 12) {
      return 'Good Morning 👋';
    } else if (hour < 17) {
      return 'Good Afternoon 👋';
    } else {
      return 'Good Evening 👋';
    }
  }
}
